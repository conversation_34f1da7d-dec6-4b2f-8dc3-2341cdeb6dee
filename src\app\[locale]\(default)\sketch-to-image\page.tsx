import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Pricing from "@/components/blocks/pricing";
import Testimonial from "@/components/blocks/testimonial";
import SketchToImage from "@/components/sketchtoimage";
import { getSketchToImageLandingPage } from "@/services/page";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/sketch-to-image`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/sketch-to-image`;
  }

  return {
    title: "Sketch to Image AI Converter - Transform Sketches into Any Style Images",
    description: "Convert your sketches and drawings into any style images using advanced AI technology. Upload your sketch and watch it come to life.",
    keywords: "sketch to image, AI art generator, sketch converter, drawing to photo, AI image generation",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function SketchToImageLandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getSketchToImageLandingPage(locale);

  return (
    <>
      {page.sketchtoimage && <SketchToImage section={page.sketchtoimage as any} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.cta && <CTA section={page.cta} />}
      {page.faq && <FAQ section={page.faq} />}
    </>
  );
}
