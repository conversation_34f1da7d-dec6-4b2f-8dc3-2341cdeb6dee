import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');
    const filename = searchParams.get('filename') || 'image.png';
    
    if (!imageUrl) {
      return new NextResponse('Missing URL parameter', { status: 400 });
    }
    
    console.log('🔄 代理下载:', imageUrl);
    
    // 获取图片
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageDownloader/1.0)',
        'Accept': 'image/*',
      }
    });
    
    if (!response.ok) {
      console.log('❌ 代理获取失败:', response.status);
      return new NextResponse(`Failed to fetch image: ${response.status}`, { 
        status: response.status 
      });
    }
    
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/png';
    
    console.log('✅ 代理下载成功:', imageBuffer.byteLength, 'bytes');
    
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache',
        'Access-Control-Allow-Origin': '*',
      },
    });
    
  } catch (error) {
    console.error('❌ 代理下载错误:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
} 