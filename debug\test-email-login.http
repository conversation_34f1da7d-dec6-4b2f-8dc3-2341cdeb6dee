# 邮箱登录功能测试脚本

### 测试发送验证邮件
POST http://localhost:3000/api/auth/email/send
Content-Type: application/json

{
  "email": "<EMAIL>"
}

### 测试无效邮箱格式
POST http://localhost:3000/api/auth/email/send
Content-Type: application/json

{
  "email": "invalid-email"
}

### 测试缺少邮箱参数
POST http://localhost:3000/api/auth/email/send
Content-Type: application/json

{
}

### 测试验证token（需要先从邮件中获取真实token）
GET http://localhost:3000/api/auth/email/verify?token=your-token-here

### 测试无效token
GET http://localhost:3000/api/auth/email/verify?token=invalid-token

### 测试缺少token参数
GET http://localhost:3000/api/auth/email/verify 