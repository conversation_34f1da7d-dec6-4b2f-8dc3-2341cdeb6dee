import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { deleteSketchResultWithTaskCleanup } from '@/models/sketch';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ resultId: string }> }
) {
  try {
    const session = await auth();
    const userUuid = session?.user?.uuid;

    if (!userUuid) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    const { resultId } = await params;

    if (!resultId) {
      return NextResponse.json({
        success: false,
        error: 'Result ID is required'
      }, { status: 400 });
    }

    // 删除图片结果（增强版：如果任务没有其他结果则删除整个任务）
    const deleteResult = await deleteSketchResultWithTaskCleanup(resultId, userUuid);

    if (!deleteResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Failed to delete image or image not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully',
      taskDeleted: deleteResult.taskDeleted,
      taskId: deleteResult.taskId,
      taskType: deleteResult.taskType
    });

  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
} 