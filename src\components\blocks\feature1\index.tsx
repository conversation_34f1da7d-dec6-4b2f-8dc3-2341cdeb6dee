"use client";

import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature1({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-16">
          {/* Single Large Image Display */}
          {section.after_image && (
  <div className="relative w-full aspect-[4/4] rounded-lg overflow-hidden bg-muted shadow-lg group">
    <img
      src={section.after_image.src}
      alt={section.after_image.alt || "Feature showcase"}
      className="w-full h-full object-cover"
    />

    {/* 显示在 hover 时 */}
    {section.after_image.alt && (
      <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-md text-sm font-medium backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {section.after_image.alt}
      </div>
    )}
  </div>
)}


          {/* Content */}
          <div className="flex flex-col lg:text-left">
            {section.label && (
              <Badge variant="outline" className="mb-4">
                {section.label}
              </Badge>
            )}
            {section.title && (
              <h2 className="text-4xl lg:text-5xl font-serif font-medium mb-6 bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-gray-100 dark:via-gray-300 dark:to-gray-100 bg-clip-text text-transparent leading-tight">
                {section.title}
              </h2>
            )}
            {section.content && (
              <div className="prose prose-gray max-w-none text-muted-foreground leading-relaxed">
                {section.content.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-4 text-base lg:text-lg">
                    {paragraph}
                  </p>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
