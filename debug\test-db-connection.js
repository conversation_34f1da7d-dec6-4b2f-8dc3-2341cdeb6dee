#!/usr/bin/env node

/**
 * 数据库连接测试脚本
 * 用于诊断 Supabase 数据库连接问题
 */

// 使用项目的数据库连接
const { db } = require('../src/db/index.js');
require('dotenv').config({ path: '.env.production' });

async function testDatabaseConnection() {
  console.log('🔍 测试数据库连接...\n');
  
  const databaseUrl = process.env.DATABASE_URL;
  console.log('📋 数据库连接信息:');
  console.log('DATABASE_URL:', databaseUrl ? databaseUrl.replace(/:[^:@]*@/, ':****@') : '未设置');
  
  if (!databaseUrl) {
    console.error('❌ 错误: DATABASE_URL 环境变量未设置');
    process.exit(1);
  }

  // 解析连接字符串
  let connectionConfig;
  try {
    connectionConfig = new URL(databaseUrl);
    console.log('\n🔗 连接参数:');
    console.log('主机:', connectionConfig.hostname);
    console.log('端口:', connectionConfig.port);
    console.log('数据库:', connectionConfig.pathname.slice(1));
    console.log('用户名:', connectionConfig.username);
    console.log('SSL模式:', connectionConfig.searchParams.get('sslmode') || '未设置');
  } catch (error) {
    console.error('❌ 错误: 无法解析数据库连接字符串');
    console.error(error.message);
    process.exit(1);
  }

  try {
    console.log('\n🔌 尝试连接数据库...');

    // 测试基本查询
    console.log('\n📊 测试基本查询...');
    const result = await db().execute('SELECT version()');
    console.log('✅ 数据库连接和查询成功!');
    console.log('数据库版本:', result[0]?.version || '未知');

    // 检查用户表是否存在
    console.log('\n🔍 检查用户表...');
    const tableCheck = await db().execute(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    if (tableCheck[0]?.exists) {
      console.log('✅ 用户表存在');

      // 检查用户数量
      const userCount = await db().execute('SELECT COUNT(*) FROM users');
      console.log(`👥 用户总数: ${userCount[0]?.count || 0}`);

    } else {
      console.log('❌ 用户表不存在');
    }

  } catch (error) {
    console.error('\n❌ 数据库连接失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误代码:', error.code);
    console.error('错误消息:', error.message);
    
    if (error.code === 'XX000') {
      console.log('\n💡 建议解决方案:');
      console.log('1. 检查 Supabase 项目是否处于活跃状态');
      console.log('2. 验证数据库密码是否正确');
      console.log('3. 确认项目 ID 是否正确');
      console.log('4. 检查 Supabase 账户是否有足够的配额');
    }
    
    if (error.code === 'ENETUNREACH') {
      console.log('\n💡 建议解决方案:');
      console.log('1. 检查网络连接');
      console.log('2. 尝试使用 Pooler 连接');
      console.log('3. 检查防火墙设置');
    }
    
    process.exit(1);
  }
}

// 运行测试
testDatabaseConnection().catch(console.error);
