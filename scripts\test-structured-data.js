/**
 * 结构化数据测试脚本
 * 用于验证页面中的JSON-LD结构化数据是否正确生成
 */

const puppeteer = require('puppeteer');

async function testStructuredData() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  const baseUrl = 'http://localhost:3001';
  const testPages = [
    { url: '/', name: '首页' },
    { url: '/pricing', name: '定价页' },
    { url: '/sketch-to-image', name: '素描转图片页' },
    { url: '/imagetoimage', name: '图片转图片页' }
  ];

  console.log('🚀 开始测试结构化数据...\n');

  for (const testPage of testPages) {
    try {
      console.log(`📄 测试页面: ${testPage.name} (${testPage.url})`);
      
      await page.goto(`${baseUrl}${testPage.url}`, { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // 获取页面中的所有JSON-LD脚本
      const jsonLdScripts = await page.evaluate(() => {
        const scripts = document.querySelectorAll('script[type="application/ld+json"]');
        return Array.from(scripts).map(script => {
          try {
            return {
              id: script.id,
              content: JSON.parse(script.textContent),
              valid: true
            };
          } catch (e) {
            return {
              id: script.id,
              content: script.textContent,
              valid: false,
              error: e.message
            };
          }
        });
      });

      console.log(`  ✅ 找到 ${jsonLdScripts.length} 个结构化数据脚本`);
      
      jsonLdScripts.forEach((script, index) => {
        if (script.valid) {
          console.log(`    📋 脚本 ${index + 1} (${script.id}): ${script.content['@type']}`);
          
          // 验证必要字段
          if (script.content['@context'] !== 'https://schema.org') {
            console.log(`      ⚠️  警告: @context 不是 https://schema.org`);
          }
          
          if (!script.content['@type']) {
            console.log(`      ❌ 错误: 缺少 @type 字段`);
          }
        } else {
          console.log(`    ❌ 脚本 ${index + 1} (${script.id}): JSON 解析错误 - ${script.error}`);
        }
      });

      console.log('');
      
    } catch (error) {
      console.log(`  ❌ 页面加载失败: ${error.message}\n`);
    }
  }

  await browser.close();
  console.log('✨ 测试完成！');
}

// 验证特定Schema类型的函数
function validateSchema(schema, type) {
  const validations = {
    WebSite: ['name', 'url'],
    Organization: ['name', 'url'],
    SoftwareApplication: ['name', 'description', 'applicationCategory'],
    Service: ['name', 'description', 'provider'],
    Product: ['name', 'description'],
    FAQPage: ['mainEntity']
  };

  const requiredFields = validations[type] || [];
  const missingFields = requiredFields.filter(field => !schema[field]);
  
  return {
    valid: missingFields.length === 0,
    missingFields
  };
}

// 如果直接运行此脚本
if (require.main === module) {
  testStructuredData().catch(console.error);
}

module.exports = { testStructuredData, validateSchema };
