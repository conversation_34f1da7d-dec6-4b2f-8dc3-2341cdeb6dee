@import "tailwindcss";
@import "./theme.css";

@plugin "tailwindcss-animate";

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.container {
  @apply mx-auto max-w-7xl px-4 md:px-8;
}

input,
select,
textarea {
  @apply border-border outline-ring/50 bg-background;
}

button {
  @apply cursor-pointer border-border outline-ring/50;
}

/* Custom range slider styles - hide native thumb */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 0;
  height: 0;
  opacity: 0;
}

input[type="range"]::-moz-range-thumb {
  width: 0;
  height: 0;
  opacity: 0;
  border: none;
  background: transparent;
  -moz-appearance: none;
}

input[type="range"]::-ms-thumb {
  width: 0;
  height: 0;
  opacity: 0;
  background: transparent;
  border: none;
}
