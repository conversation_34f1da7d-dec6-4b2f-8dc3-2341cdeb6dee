import { Pathnames } from "next-intl/routing";

export const locales = ["en", "zh", "zh-TW", "ja", "ko", "ru", "fr", "de", "es", "it"];

export const localeNames: any = {
  en: "English",
  zh: "中文",
  "zh-TW": "繁體中文",
  ja: "日本語",
  ko: "한국어",
  ru: "Русский",
  fr: "Français",
  de: "Deutsch",
  es: "Español",
  it: "Italiano",
};

export const defaultLocale = "en";

export const localePrefix = "as-needed";

export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";
