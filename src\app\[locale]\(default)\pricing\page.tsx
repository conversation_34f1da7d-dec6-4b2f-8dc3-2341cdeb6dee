import Pricing from "@/components/blocks/pricing";
import FAQ from "@/components/blocks/faq";
import { getPricingPage } from "@/services/page";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getPricingPage(locale);
  
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/pricing`;
  }

  return {
    title: page.title || "Pricing Plans - ImageToSketch.ai",
    description: page.description || "Choose the perfect plan for your sketch generation needs. Get unlimited access to AI-powered photo to sketch conversion with commercial rights included.",
    keywords: page.keywords || "pricing, plans, subscription, photo to sketch, AI sketch, commercial license, credits",
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: page.title || "Pricing Plans - ImageToSketch.ai",
      description: page.description || "Choose the perfect plan for your sketch generation needs. Get unlimited access to AI-powered photo to sketch conversion with commercial rights included.",
      url: canonicalUrl,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: page.title || "Pricing Plans - ImageToSketch.ai",
      description: page.description || "Choose the perfect plan for your sketch generation needs. Get unlimited access to AI-powered photo to sketch conversion with commercial rights included.",
    },
  };
}

export default async function PricingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getPricingPage(locale);

  return (
    <>
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.faq && <FAQ section={page.faq} />}
    </>
  );
}
