/**
 * 简单的结构化数据检查脚本
 * 检查页面是否包含预期的结构化数据
 */

const https = require('https');
const http = require('http');

const baseUrl = 'http://localhost:3000';

const testPages = [
  { 
    url: '/', 
    name: '首页',
    expectedSchemas: ['WebSite', 'Organization', 'SoftwareApplication', 'Service']
  },
  { 
    url: '/pricing', 
    name: '定价页',
    expectedSchemas: ['WebSite', 'Organization', 'Product']
  },
  { 
    url: '/sketch-to-image', 
    name: '素描转图片页',
    expectedSchemas: ['WebSite', 'Organization', 'Service']
  },
  { 
    url: '/imagetoimage', 
    name: '图片转图片页',
    expectedSchemas: ['WebSite', 'Organization', 'Service']
  }
];

function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve(data);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

function extractJsonLd(html) {
  const jsonLdRegex = /<script[^>]*type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis;
  const matches = [];
  let match;
  
  while ((match = jsonLdRegex.exec(html)) !== null) {
    try {
      const jsonData = JSON.parse(match[1]);
      matches.push(jsonData);
    } catch (e) {
      console.log('JSON 解析错误:', e.message);
    }
  }
  
  return matches;
}

async function checkPage(testPage) {
  try {
    console.log(`\n📄 检查页面: ${testPage.name} (${testPage.url})`);
    
    const html = await fetchPage(`${baseUrl}${testPage.url}`);
    const jsonLdData = extractJsonLd(html);
    
    console.log(`  ✅ 找到 ${jsonLdData.length} 个结构化数据脚本`);
    
    const foundSchemas = jsonLdData.map(data => data['@type']).filter(Boolean);
    
    jsonLdData.forEach((data, index) => {
      console.log(`    📋 脚本 ${index + 1}: ${data['@type'] || '未知类型'}`);
      
      // 基本验证
      if (data['@context'] !== 'https://schema.org') {
        console.log(`      ⚠️  警告: @context 应该是 https://schema.org`);
      }
      
      if (!data['@type']) {
        console.log(`      ❌ 错误: 缺少 @type 字段`);
      }
    });
    
    // 检查期望的 Schema 类型
    console.log(`\n  📊 Schema 类型检查:`);
    testPage.expectedSchemas.forEach(expectedType => {
      if (foundSchemas.includes(expectedType)) {
        console.log(`    ✅ ${expectedType}: 已找到`);
      } else {
        console.log(`    ❌ ${expectedType}: 未找到`);
      }
    });
    
    return {
      success: true,
      schemasFound: jsonLdData.length,
      foundSchemas
    };
    
  } catch (error) {
    console.log(`  ❌ 检查失败: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

async function runCheck() {
  console.log('🚀 开始检查结构化数据...');
  console.log(`📍 基础URL: ${baseUrl}`);
  
  let totalSuccess = 0;
  let totalSchemas = 0;
  
  for (const testPage of testPages) {
    const result = await checkPage(testPage);
    if (result.success) {
      totalSuccess++;
      totalSchemas += result.schemasFound;
    }
    
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`\n📈 检查总结:`);
  console.log(`  ✅ 成功检查页面: ${totalSuccess}/${testPages.length}`);
  console.log(`  📋 总结构化数据脚本: ${totalSchemas}`);
  
  console.log(`\n💡 下一步建议:`);
  console.log(`  1. 使用 Google Rich Results Test 验证: https://search.google.com/test/rich-results`);
  console.log(`  2. 使用 Schema.org Validator 验证: https://validator.schema.org/`);
  console.log(`  3. 在 Google Search Console 中监控结构化数据状态`);
  
  console.log('\n✨ 检查完成！');
}

// 运行检查
runCheck().catch(console.error);
