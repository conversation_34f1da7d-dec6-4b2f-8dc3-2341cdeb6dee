CREATE TABLE "sketch_results" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "sketch_results_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"task_uuid" varchar(255) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"original_image_url" varchar(500),
	"result_image_url" varchar(500) NOT NULL,
	"status" varchar(50) NOT NULL,
	"r2_key" varchar(500) NOT NULL,
	"file_size" integer,
	"processing_time" integer,
	CONSTRAINT "sketch_results_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE "sketch_tasks" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "sketch_tasks_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"status" varchar(50) NOT NULL,
	"style" varchar(100) NOT NULL,
	"aspect_ratio" varchar(20) NOT NULL,
	"original_image_count" integer NOT NULL,
	"completed_count" integer DEFAULT 0,
	"error_message" text,
	"total_credits_used" integer DEFAULT 0,
	CONSTRAINT "sketch_tasks_uuid_unique" UNIQUE("uuid")
);
