import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  // 判断是否为emoji的简单函数
  const isEmoji = (str: string) => {
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    return emojiRegex.test(str) || str.length === 1 || str.length === 2;
  };

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="mx-auto flex max-w-4xl flex-col items-center gap-2 text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-serif font-medium mb-6 bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-gray-100 dark:via-gray-300 dark:to-gray-100 bg-clip-text text-transparent leading-tight">
            {section.title}
          </h2>
          <p className="max-w-2xl text-lg text-muted-foreground leading-relaxed">
            {section.description}
          </p>
        </div>
        
        {/* 竖直排列的卡片 */}
        <div className="max-w-4xl mx-auto space-y-8">
          {section.items?.map((item, i) => (
            <div key={i} className="bg-card border border-border rounded-xl p-8 shadow-sm hover:shadow-md transition-all duration-300 group">
              <div className="flex items-start gap-6">
                {/* Icon - 支持emoji和普通图标 */}
                {item.icon && (
                  <div className="flex-shrink-0 w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                    {isEmoji(item.icon) ? (
                      <span className="text-3xl">{item.icon}</span>
                    ) : (
                      <Icon name={item.icon} className="w-8 h-8 text-primary" />
                    )}
                  </div>
                )}
                
                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-2xl font-medium mb-4 leading-tight group-hover:text-primary transition-colors duration-300" style={{ fontFamily: 'Georgia, serif' }}>
                    {item.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed text-lg">
                    {item.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
