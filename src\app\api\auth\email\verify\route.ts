import { NextRequest, NextResponse } from "next/server";
import { signIn } from "@/auth";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get("token");

    if (!token) {
      return NextResponse.redirect(new URL("/auth/signin?error=missing-token", request.url));
    }

    // 使用NextAuth的signIn函数来登录
    try {
      const result = await signIn("email-login", {
        token,
        redirect: false,
      });

      if (result?.error) {
        return NextResponse.redirect(new URL("/auth/signin?error=verification-failed", request.url));
      }

      // 登录成功，重定向到首页
      return NextResponse.redirect(new URL("/", request.url));
    } catch (error) {
      console.error("Email verification signin error:", error);
      return NextResponse.redirect(new URL("/auth/signin?error=verification-failed", request.url));
    }

  } catch (error) {
    console.error("Email verification error:", error);
    return NextResponse.redirect(new URL("/auth/signin?error=verification-failed", request.url));
  }
} 