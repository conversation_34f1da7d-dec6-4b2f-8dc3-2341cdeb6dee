"use client";

import * as React from "react";

import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { SiGithub, SiGmail, SiGoogle } from "react-icons/si";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { signIn } from "next-auth/react";
import { useAppContext } from "@/contexts/app";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

export default function SignModal() {
  const t = useTranslations();
  const { showSignModal, setShowSignModal } = useAppContext();

  const [open, setOpen] = React.useState(false);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <Dialog open={showSignModal} onOpenChange={setShowSignModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-2xl font-semibold text-center">
              {t("sign_modal.sign_in_title")}
            </DialogTitle>
            <DialogDescription className="text-center text-muted-foreground">
              {t("sign_modal.sign_in_description")}
            </DialogDescription>
          </DialogHeader>
          <ProfileForm />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={showSignModal} onOpenChange={setShowSignModal}>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DrawerTitle>{t("sign_modal.sign_in_title")}</DrawerTitle>
          <DrawerDescription>
            {t("sign_modal.sign_in_description")}
          </DrawerDescription>
        </DrawerHeader>
        <ProfileForm className="px-4" />
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline">{t("sign_modal.cancel_title")}</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

function ProfileForm({ className }: React.ComponentProps<"form">) {
  const t = useTranslations();
  const { setShowSignModal } = useAppContext();
  const [email, setEmail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [showSuccessModal, setShowSuccessModal] = React.useState(false);

  const handleEmailSignIn = async () => {
    if (!email || !email.includes("@")) {
      toast.error(t("sign_modal.invalid_email"));
      return;
    }

    try {
      setIsLoading(true);
      
      const response = await fetch("/api/auth/email/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 显示成功页面
        setShowSuccessModal(true);
      } else {
        toast.error(data.error || t("sign_modal.magic_link_error"));
      }
    } catch (error) {
      console.error("Email sign in error:", error);
      toast.error(t("sign_modal.something_wrong"));
    } finally {
      setIsLoading(false);
    }
  };

  // 成功页面
  if (showSuccessModal) {
    return (
      <div className="text-center space-y-6 py-8">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
          <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
          </svg>
        </div>
        
        <div className="space-y-3">
          <h2 className="text-2xl font-semibold">{t("sign_modal.check_email_title")}</h2>
          <p className="text-muted-foreground">
            {t("sign_modal.check_email_description")}
          </p>
          <p className="font-medium text-primary">{email}</p>
          <p className="text-sm text-muted-foreground">Image To Sketch AI</p>
        </div>

        <Button 
          onClick={() => {
            setShowSuccessModal(false);
            setShowSignModal(false);
            setEmail("");
          }}
          className="w-full"
        >
          {t("sign_modal.close_title")}
        </Button>
      </div>
    );
  }

  // 检查环境变量
  const isGoogleEnabled = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true";
  const isGithubEnabled = process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true";
  const isEmailEnabled = process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true";

  // 调试信息（仅在开发环境显示）
  if (process.env.NODE_ENV === "development") {
    console.log("Auth Environment Variables:", {
      NEXT_PUBLIC_AUTH_GOOGLE_ENABLED: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED,
      NEXT_PUBLIC_AUTH_GITHUB_ENABLED: process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED,
      NEXT_PUBLIC_AUTH_EMAIL_ENABLED: process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED,
    });
  }

  // 原有的登录表单...
  return (
    <div className={cn("grid items-start gap-4", className)}>
      {/* Google Sign In - 默认启用作为备用方案 */}
      {(isGoogleEnabled || (!isGoogleEnabled && !isGithubEnabled && !isEmailEnabled)) && (
        <Button
          variant="outline"
          className="w-full flex items-center justify-center gap-2 h-12"
          onClick={() => {
            signIn("google");
          }}
        >
          <SiGoogle className="w-5 h-5" />
          {t("sign_modal.google_sign_in")}
        </Button>
      )}

      {/* GitHub Sign In */}
      {process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" && (
        <Button
          variant="outline"
          className="w-full flex items-center gap-2"
          onClick={() => {
            signIn("github");
          }}
        >
          <SiGithub className="w-4 h-4" />
          {t("sign_modal.github_sign_in")}
        </Button>
      )}

      {/* Divider */}
      {process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true" && (
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-muted-foreground/20" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              {t("sign_modal.or")}
            </span>
          </div>
        </div>
      )}

      {/* Email Input */}
      {process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true" && (
        <div className="space-y-4">
          <Input
            type="email"
            placeholder={t("sign_modal.email_placeholder")}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="h-12"
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleEmailSignIn();
              }
            }}
          />
          
          <Button
            onClick={handleEmailSignIn}
            disabled={isLoading || !email}
            className="w-full h-12 bg-muted-foreground hover:bg-muted-foreground/90 text-background"
          >
            {isLoading ? t("sign_modal.sending") : t("sign_modal.send_magic_link")}
          </Button>
        </div>
      )}
    </div>
  );
}
