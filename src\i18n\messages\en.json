{"metadata": {"title": "Convert Image to Sketch Online with AI | ImageToSketch.ai", "description": "Turn any photo into a stunning sketch drawing with our AI-powered image to sketch converter. Instantly transform images into pencil, line art, or artistic sketches online.", "keywords": "image to sketch, convert photo to sketch, photo to line art, sketch converter, AI sketch generator, image to drawing, AI photo sketch, pencil sketch from image, image sketch online"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "my_creations": "My Creations", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Please continue by sign in", "sign_in_description": "You will receive a magic link in your email", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "<EMAIL>", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "OR CONTINUE WITH", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel", "send_magic_link": "Send me my link", "sending": "Sending...", "magic_link_sent": "Magic link sent! Check your email to sign in.", "magic_link_error": "Failed to send magic link. Please try again.", "invalid_email": "Please enter a valid email address", "something_wrong": "Something went wrong. Please try again.", "check_email_title": "Check your email", "check_email_description": "We sent a magic link to your email. Click the link to sign in."}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "credits": {"remaining": ""}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "my_creations": {"title": "My Creations", "description": "View and manage all your AI-generated sketches", "metadata": {"title": "My AI Art Creations - View & Manage Generated Images | ImageToSketch.ai", "description": "View, manage and download all your AI-generated sketches and artwork. Access your complete gallery of transformed images with various artistic styles.", "keywords": "my AI art, generated sketches, AI artwork gallery, manage creations, download AI images, sketch collection, AI art history"}, "filters": {"all_styles": "All Styles", "all_status": "All Status", "styles": {"pencil": "Pencil", "charcoal": "Charc<PERSON>l", "sketcher": "<PERSON><PERSON><PERSON>", "comicnoir": "Comic Noir", "crosshatch": "Crosshatch", "colorsketch": "Color Sketch", "crayon": "Crayon", "penart": "Pen Art", "inkwash": "Ink Was<PERSON>", "graphicnovel": "Graphic Novel"}, "status": {"completed": "Completed", "generating": "Generating", "error": "Error"}}, "actions": {"load_more": "Load More", "delete_image": "Delete image", "click_to_preview": "Click to preview", "download": "Download", "delete": "Delete", "cancel": "Cancel", "close": "Close"}, "preview": {"title": "{style} Style Sketch", "close_hint": "Click outside to close"}, "delete_confirm": {"title": "Delete Image", "message": "Are you sure you want to delete this image? This action cannot be undone.", "image_alt": "Image to delete"}, "empty": {"message": "No creations found"}, "loading": "Loading..."}, "imagetoimage": {"name": "imagetoimage", "title": "Image to Image AI Generator", "description": "<PERSON><PERSON>ss AI to morph, stylize, and recreate any image effortlessly", "upload": {"title": "Image Upload", "description": "Upload an image to use as a reference, maximum 5 images allowed.", "button": "Upload Image", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "Uploaded Images"}, "prompt": {"title": "Prompt", "description": "Describe what you want to change in the image", "placeholder": "Example: Change the background to a blue sky.", "maxLength": "1000"}, "aspectRatio": {"title": "Aspect Ratio", "description": "Choose the aspect ratio you want to use", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "outputs": {"title": "Number of Outputs", "description": "Choose the number of outputs you want to generate"}, "generate": {"button": "Generate Images", "generating": "Generating...", "icon": "✨"}, "preview": {"empty": {"title": "Upload an image to get started", "description": "Upload an image and describe the changes you want to see to generate amazing results"}, "navigation": {"of": "of"}, "download": "Download image", "transformLabel": "Transformed into Ghibli style"}, "messages": {"maxImagesError": "Maximum {max} images allowed", "fileTooLarge": "File {name} is too large. Maximum size is {size}MB", "formatNotSupported": "File {name} format not supported", "uploadSuccess": "{count} image(s) uploaded successfully", "noImages": "Please upload at least one image", "noPrompt": "Please enter a prompt", "generateSuccess": "Images generated successfully!", "generateError": "Failed to generate images. Please try again."}}, "sketchtoimage": {"name": "sketchtoimage", "title": "Sketch to Image AI Converter", "description": "Transform your sketches and photos into photorealistic images with advanced AI technology", "disabled": false, "header": {"title": "Sketch to Image AI", "description": "Transform your sketches into realistic images using AI"}, "upload": {"title": "Sketch Upload", "description": "Upload your sketch or drawing to transform into a realistic image, maximum 3 sketches allowed.", "button": "Upload Sketch", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "Uploaded Sketches", "file_types": "JPG, PNG, WebP", "max_size": "Max {maxSize}MB", "uploaded_count": "Uploaded ({count}/{max})", "add_more": "Add More", "max_images_error": "Maximum {max} sketches allowed", "invalid_file_type": "{filename} is not a valid image file", "file_size_error": "File size exceeds {maxSize}MB", "success": "{filename} uploaded successfully", "failed": "{filename} upload failed"}, "style": {"title": "Choose Style", "description": "Select the artistic style for your generated images", "more_styles": "More Styles", "all_styles": "All Styles", "style": "Style", "options": {"architecture": {"label": "Architecture", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/architecture.png", "description": "Architectural and technical drawing style"}, "default": {"label": "<PERSON><PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/default.png", "description": "Standard realistic conversion"}, "photorealistic": {"label": "Photorealistic", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/Photorealistic.png", "description": "Ultra-realistic photo-like results"}, "digitalart": {"label": "Digital Art", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/digitalart.png", "description": "Modern digital artwork style"}, "anime": {"label": "Anime", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/anime.png", "description": "Japanese anime illustration style"}, "interiordesign": {"label": "Interior Design", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/interiordesign.png", "description": "Interior design visualization"}, "3d": {"label": "3D", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/3d.png", "description": "Three-dimensional rendering"}, "pixar": {"label": "Pixar", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/pixar.png", "description": "Pixar animation style"}, "fantasy": {"label": "Fantasy", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/fantasy.png", "description": "Fantasy art and magical themes"}, "rpg": {"label": "RPG", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/rpg.png", "description": "Role-playing game character art"}, "comicbook": {"label": "Comic Book", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/comicbook.png", "description": "Comic book illustration style"}, "clay": {"label": "<PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/clay.png", "description": "Clay sculpture and modeling"}, "vectorart": {"label": "Vector Art", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/vectorart.png", "description": "Clean vector illustration style"}, "minimalist": {"label": "Minimalist", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/minimalist.png", "description": "Simple and clean design"}, "watercolor": {"label": "Watercolor", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/watercolor.png", "description": "Watercolor painting technique"}, "oilpainting": {"label": "Oil Painting", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/oilpainting.png", "description": "Classic oil painting style"}, "gta": {"label": "GTA", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/gta.png", "description": "Grand Theft Auto game style"}, "minecraft": {"label": "Minecraft", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/minecraft.png", "description": "Minecraft block-based style"}}}, "creativity": {"title": "(Optional) Adjust Creativity", "description": "Control how creative the AI should be with your sketch", "similar": "Similar", "creative": "Creative", "current": "Current", "balanced": "Balanced", "slider_description": "Move the slider to adjust creativity level"}, "aspectRatio": {"title": "Aspect Ratio", "description": "Choose the aspect ratio for your generated image", "ratio": "<PERSON><PERSON>", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "generate": {"button": "Generate Image", "generating": "Generating...", "icon": "🎨", "button_with_credits": "Generate Image ({credits} credits)", "insufficient_credits": "Insufficient credits", "need_credits": "Need {credits} credits", "no_images_error": "Please upload at least one sketch", "insufficient_credits_error": "Insufficient credits, please top up", "success": "Image generated successfully!", "failed": "Generation failed, please try again", "network_error": "Generation failed, please check network connection", "server_busy": "Server is busy, please try again later", "regenerate_success": "Regeneration successful!", "regenerate_failed": "Regeneration failed", "regenerate_network_error": "Regeneration failed, please try again"}, "delete_task": {"success": "Task deleted", "failed": "Delete failed"}, "download": {"success": "Image downloaded successfully!", "trying_to_download": "Trying to download image...", "cannot_direct_download": "Cannot download directly, please right-click to save", "link_copied": "Image link copied to clipboard", "failed": "Download failed"}, "time": {"just_now": "Just now", "minutes_ago": "{minutes} minutes ago", "hours_ago": "{hours} hours ago", "days_ago": "{days} days ago"}, "preview": {"empty": {"title": "Upload a sketch to get started", "description": "Upload your sketch and select a style to generate amazing realistic images"}, "navigation": {"of": "of"}, "download": "Download image", "transformLabel": "Transformed to realistic image", "generated_sketch": "Generated ", "modal": {"title": "Image Preview", "close": "Close", "download": "Download", "close_hint": "Click outside or press ESC to close", "downloadFilename": "generated_image_{timestamp}.png"}}, "actions": {"add_more": "Add More", "add": "Add", "preview": "Preview", "download": "Download", "retry": "Retry", "regenerate": "Regenerate", "open_image": "Open Image", "close": "Close", "test_url": "Test URL"}, "status": {"generating": "Generating", "completed": "Completed", "error": "Error"}, "alt_text": {"generated_image": "Generated realistic image", "uploaded_sketch": "Uploaded sketch", "generated_sketch": "Generated sketch"}, "tooltips": {"preview_image": "Preview image", "download_image": "Download image", "delete_image": "Delete image"}, "errors": {"failed_to_load": "Failed to load"}, "messages": {"maxImagesError": "Maximum {max} sketches allowed", "fileTooLarge": "File {name} exceeds {size}MB size limit", "formatNotSupported": "File {name} format not supported", "uploadSuccess": "Successfully uploaded {count} sketches", "noImages": "Please upload at least one sketch", "generateSuccess": "Image generated successfully", "generateError": "Failed to generate image"}, "examples": {"panel_title": "Examples", "recent_tasks_title": "Recent Tasks", "view_all": "View All", "no_tasks": "No tasks yet", "no_tasks_description": "Generate your first sketch to see it here", "loading_history": "Loading history...", "cta": {"title": "Want to convert your own sketches to images?", "button": "Sign in to get started"}, "items": [{"url": "/imgs/sketchtoimage/examples/example1.png", "title": "Portrait Generation", "description": "Transform portrait sketches into realistic photos"}, {"url": "/imgs/sketchtoimage/examples/example2.png", "title": "Landscape Creation", "description": "Convert landscape drawings to stunning photos"}, {"url": "/imgs/sketchtoimage/examples/example3.png", "title": "Architecture Visualization", "description": "Turn architectural sketches into realistic renders"}]}}}