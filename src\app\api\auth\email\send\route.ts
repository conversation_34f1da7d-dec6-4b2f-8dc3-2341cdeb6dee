import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailVerificationTokens } from "@/db/schema";
import { eq } from "drizzle-orm";
import { Resend } from "resend";
import { getUuid } from "@/lib/hash";

// 延迟初始化Resend实例
function getResendClient() {
  if (!process.env.RESEND_API_KEY) {
    throw new Error("RESEND_API_KEY environment variable is not set");
  }
  return new Resend(process.env.RESEND_API_KEY);
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 });
    }

    // 生成验证token
    const token = getUuid();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15分钟后过期

    // 清理该邮箱的旧token
    await db()
      .delete(emailVerificationTokens)
      .where(eq(emailVerificationTokens.email, email));

    // 保存新token
    await db().insert(emailVerificationTokens).values({
      email,
      token,
      expires_at: expiresAt,
    });

    // 发送邮件
    const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
    const verificationUrl = `${baseUrl}/api/auth/email/verify?token=${token}`;

    const resend = getResendClient();
    await resend.emails.send({
      from: process.env.NEXT_PUBLIC_EMAIL_FROM!,
      to: email,
      subject: "Sign in to your account",
      html: `
        <h2>Sign in to your account</h2>
        <p>Click the link below to sign in:</p>
        <a href="${verificationUrl}" style="display: inline-block; padding: 12px 24px; background-color: #4F46E5; color: white; text-decoration: none; border-radius: 6px; font-weight: 500;">
          Sign in
        </a>
        <p>This link will expire in 15 minutes.</p>
        <p>If you didn't request this, please ignore this email.</p>
      `,
    });

    return NextResponse.json({ 
      message: "Verification email sent successfully",
      success: true 
    });

  } catch (error) {
    console.error("Send email error:", error);
    return NextResponse.json(
      { error: "Failed to send verification email" },
      { status: 500 }
    );
  }
} 