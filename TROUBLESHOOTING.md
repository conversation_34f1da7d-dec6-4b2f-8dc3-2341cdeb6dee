# 邮箱登录功能 - 故障排除指南

## 常见问题

### 1. 数据库表结构问题

**错误信息：**
```
column "used" of relation "email_verification_tokens" does not exist
```

**原因：** 数据库表结构与代码定义不匹配。

**解决方案：**
已修复代码以匹配简化的表结构。当前表结构：
```sql
CREATE TABLE email_verification_tokens (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. 环境变量配置问题

**检查环境变量：**
```bash
node debug/check-email-config.js
```

**必需的环境变量：**
- `NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true`
- `RESEND_API_KEY=re_your_api_key`
- `NEXT_PUBLIC_EMAIL_FROM=<EMAIL>`
- `NEXTAUTH_URL=http://localhost:3000`
- `NEXTAUTH_SECRET=your_secret_key`

### 3. 邮件发送问题

**常见原因：**
- Resend API Key 无效
- 发送域名未验证
- 邮件地址格式错误

**解决步骤：**
1. 在 Resend 控制台验证域名
2. 确认 API Key 有效
3. 检查发送邮箱地址格式

### 4. Token 验证问题

**原因：**
- Token 已过期（15分钟）
- Token 已被使用（使用后立即删除）
- 数据库连接问题

**解决方案：**
- 重新请求新的验证邮件
- 检查数据库连接状态
- 清理过期的 token

### 5. 翻译键缺失

**错误信息：**
```
sign_modal.check_email_title
sign_modal.check_email_description
```

**解决方案：**
已添加缺失的翻译键到 `src/i18n/messages/en.json` 和 `src/i18n/messages/zh.json`

## 调试步骤

1. **检查环境变量**
   ```bash
   node debug/check-email-config.js
   ```

2. **验证表结构**
   ```sql
   \d email_verification_tokens
   ```

3. **测试 API 端点**
   使用 `debug/test-email-login.http` 测试

4. **查看日志**
   检查控制台输出和错误信息

5. **重启服务**
   修改环境变量后重启开发服务器

## 安全说明

- Token 使用后立即删除（而不是标记为已使用）
- 过期 token 自动清理
- 15分钟过期时间
- 唯一性约束防止重复 token

## 获取帮助

如果问题仍然存在：
1. 检查控制台完整错误信息
2. 确认数据库表结构正确
3. 验证所有环境变量已正确设置
4. 查看详细文档：`EMAIL_LOGIN_SETUP.md` 