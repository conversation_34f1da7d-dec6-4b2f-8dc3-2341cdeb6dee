'use client';

import { useState, useEffect } from 'react';
import { Download, X, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

interface Creation {
  task: {
    uuid: string;
    style: string;
    aspect_ratio: string;
    created_at: string;
    status: string;
    original_image_count: number;
    completed_count: number;
  };
  results: Array<{
    uuid: string;
    result_image_url: string;
    status: string;
    created_at: string;
  }>;
}

interface PreviewModal {
  isOpen: boolean;
  imageUrl: string;
  resultId: string;
  style: string;
}

interface DeleteConfirmModal {
  isOpen: boolean;
  resultId: string;
  imageUrl: string;
}

export default function MyCreationsClient() {
  const t = useTranslations('my_creations');
  const [creations, setCreations] = useState<Creation[]>([]);
  const [loading, setLoading] = useState(true);
  const [previewModal, setPreviewModal] = useState<PreviewModal>({
    isOpen: false,
    imageUrl: '',
    resultId: '',
    style: '',
  });
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<DeleteConfirmModal>({
    isOpen: false,
    resultId: '',
    imageUrl: '',
  });
  const [filters, setFilters] = useState({
    style: '',
    status: '',
  });
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  // 格式化图片URL - 修复跨域问题
  const formatImageUrl = (url: string): string => {
    if (!url) return '';
    
    console.log('My Creations - Original URL:', url); // 调试信息
    
    // 如果已经有协议，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    // 处理 imagetosketch.ai R2 域名
    if (url.startsWith('r2.imagetosketch.ai')) {
      return `https://${url}`;
    }
    
    // 如果是 /r2.imagetosketch.ai 开头（被当作相对路径）
    if (url.startsWith('/r2.imagetosketch.ai')) {
      return `https://${url.substring(1)}`;
    }
    
    // 兼容旧的 erniex1.org 域名
    if (url.startsWith('erniex1.org')) {
      return `https://${url}`;
    }
    
    if (url.startsWith('/erniex1.org')) {
      return `https://${url.substring(1)}`;
    }
    
    console.log('My Creations - Formatted URL:', url); // 调试信息
    // 其他情况保持原样
    return url;
  };

  // 加载数据
  const loadCreations = async (isLoadMore = false) => {
    try {
      const currentPage = isLoadMore ? page + 1 : 1;
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(filters.style && { style: filters.style }),
        ...(filters.status && { status: filters.status }),
      });

      const response = await fetch(`/api/my-creations?${params}`);
      const data = await response.json();

      if (data.success) {
        if (isLoadMore) {
          setCreations(prev => [...prev, ...data.data.creations]);
          setPage(currentPage);
        } else {
          setCreations(data.data.creations);
          setPage(1);
        }
        setHasMore(data.data.hasMore);
      }
    } catch (error) {
      console.error('Error loading creations:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCreations();
  }, [filters.style, filters.status]);

  // 预览图片
  const previewImage = (url: string, resultId: string, style: string) => {
    setPreviewModal({
      isOpen: true,
      imageUrl: formatImageUrl(url),
      resultId,
      style,
    });
  };

  // 下载图片 - 复用imagetosketch的下载逻辑
  const downloadImage = async (url: string, filename: string) => {
    try {
      const formattedUrl = formatImageUrl(url);
      
      // 使用代理API获取图片，避免CORS问题
      const proxyUrl = `/api/proxy-download?url=${encodeURIComponent(formattedUrl)}`;
      
      console.log('下载图片:', proxyUrl);
      
      // 使用 fetch 获取图片
      const response = await fetch(proxyUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // 将响应转换为 blob
      const blob = await response.blob();
      
      // 创建 blob URL
      const blobUrl = URL.createObjectURL(blob);
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;
      link.style.display = 'none';
      
      // 执行下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理 blob URL
      setTimeout(() => {
        URL.revokeObjectURL(blobUrl);
      }, 100);
      
      console.log('下载成功');
      
    } catch (error) {
      console.error('下载失败:', error);
      // 如果下载失败，打开新窗口作为备选
      window.open(formatImageUrl(url), '_blank');
    }
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (resultId: string, imageUrl: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      resultId,
      imageUrl: formatImageUrl(imageUrl),
    });
  };

  // 执行删除图片
  const executeDelete = async (resultId: string) => {
    try {
      const response = await fetch(`/api/my-creations/${resultId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        
        // 关闭模态框
        setDeleteConfirmModal({
          isOpen: false,
          resultId: '',
          imageUrl: '',
        });
        
        // 刷新列表
        loadCreations();
        
        // 如果任务被删除，通知用户并刷新 recent tasks
        if (result.taskDeleted) {
          console.log('MyCreations: Task deleted successfully!');
          console.log('MyCreations: Task ID:', result.taskId);
          console.log('MyCreations: Task Type:', result.taskType);
          
          // 通过 localStorage 或其他方式通知其他组件刷新
          // 使用 CustomEvent 来通知其他组件
          const eventDetail = { 
            taskId: result.taskId,
            taskType: result.taskType || 'image-to-sketch' // 默认为 image-to-sketch
          };
          
          console.log('MyCreations: Dispatching task-deleted event with detail:', eventDetail);
          window.dispatchEvent(new CustomEvent('task-deleted', {
            detail: eventDetail
          }));
        }
        
        console.log('Image deleted successfully');
      } else {
        console.error('Failed to delete image');
      }
    } catch (error) {
      console.error('Error deleting image:', error);
    }
  };

  if (loading) {
    return <div className="flex justify-center py-12">
      <div className="flex flex-col items-center gap-3">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="text-gray-500 text-sm">{t('loading')}</p>
      </div>
    </div>;
  }

  // 获取所有图片（扁平化）
  const allImages = creations.flatMap(creation => 
    creation.results.map(result => ({
      ...result,
      style: creation.task.style,
      taskId: creation.task.uuid,
    }))
  );

  return (
    <div className="space-y-6">
      {/* 筛选控制 */}
      {/* <div className="flex gap-3 p-4 bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-xl border border-gray-200">
                  <select 
            value={filters.style} 
            onChange={(e) => setFilters(prev => ({ ...prev, style: e.target.value }))}
            className="border border-gray-300 rounded-lg px-4 py-2 bg-white shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
          >
            <option value="">{t('filters.all_styles')}</option>
            <option value="pencil">{t('filters.styles.pencil')}</option>
            <option value="charcoal">{t('filters.styles.charcoal')}</option>
            <option value="sketcher">{t('filters.styles.sketcher')}</option>
            <option value="comicnoir">{t('filters.styles.comicnoir')}</option>
            <option value="crosshatch">{t('filters.styles.crosshatch')}</option>
            <option value="colorsketch">{t('filters.styles.colorsketch')}</option>
            <option value="crayon">{t('filters.styles.crayon')}</option>
            <option value="penart">{t('filters.styles.penart')}</option>
            <option value="inkwash">{t('filters.styles.inkwash')}</option>
            <option value="graphicnovel">{t('filters.styles.graphicnovel')}</option>
          </select>
          <select 
            value={filters.status} 
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="border border-gray-300 rounded-lg px-4 py-2 bg-white shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
          >
            <option value="">{t('filters.all_status')}</option>
            <option value="completed">{t('filters.status.completed')}</option>
            <option value="generating">{t('filters.status.generating')}</option>
            <option value="error">{t('filters.status.error')}</option>
          </select>
      </div> */}

      {/* 图片网格 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {allImages.map((image) => (
          <div key={image.uuid} className="group relative bg-gradient-to-b from-gray-50 to-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg hover:border-gray-300 transition-all duration-200">
            {/* 删除按钮 - 右上角 */}
                         <button
               onClick={(e) => {
                 e.stopPropagation();
                 showDeleteConfirm(image.uuid, image.result_image_url);
               }}
               className="absolute top-2 right-2 z-10 p-1.5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 hover:bg-red-600 transition-all duration-200"
               title={t('actions.delete_image')}
             >
              <Trash2 className="w-3 h-3" />
            </button>

            {/* 图片 - 点击预览 */}
            <div 
              className="aspect-square relative cursor-pointer"
              onClick={() => previewImage(image.result_image_url, image.uuid, image.style)}
            >
              <Image
                src={formatImageUrl(image.result_image_url)}
                alt={`${image.style} sketch`}
                fill
                className="object-cover"
                unoptimized
              />
              
              {/* 悬停覆盖层 */}
                             <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                 <div className="text-white text-sm font-medium bg-black/50 px-3 py-1 rounded-full">
                   {t('actions.click_to_preview')}
                 </div>
               </div>
            </div>
          </div>
        ))}
      </div>

      {/* 加载更多 */}
      {hasMore && (
        <div className="text-center">
          <button
            onClick={() => loadCreations(true)}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t('actions.load_more')}
          </button>
        </div>
      )}

      {/* 空状态 */}
             {allImages.length === 0 && (
         <div className="text-center py-12">
           <p className="text-gray-500">{t('empty.message')}</p>
         </div>
       )}

      {/* 预览模态框 */}
      {previewModal.isOpen && (
        <div 
          className="fixed inset-0 bg-black/85 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setPreviewModal({ isOpen: false, imageUrl: '', resultId: '', style: '' })}
        >
          {/* 模态框容器 */}
          <div 
            className="relative max-w-4xl max-h-[85vh] w-full bg-white rounded-xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部标题栏 */}
                         <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
               <h3 className="font-semibold text-gray-900 truncate pr-4">
                 {t('preview.title', { style: previewModal.style })}
               </h3>
               {/* 关闭按钮 */}
               <button
                 onClick={() => setPreviewModal({ isOpen: false, imageUrl: '', resultId: '', style: '' })}
                 className="flex-shrink-0 w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-full transition-all"
                 title={t('actions.close')}
               >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            {/* 图片容器 */}
            <div className="relative bg-gray-100 flex items-center justify-center">
              <img
                src={previewModal.imageUrl}
                alt={`${previewModal.style} sketch`}
                className="w-full h-auto max-h-[65vh] object-contain"
              />
            </div>
            
            {/* 底部操作区 */}
            <div className="p-4 bg-gray-50 border-t">
                             <div className="flex items-center justify-between">
                 <span className="text-sm text-gray-500">
                   {t('preview.close_hint')}
                 </span>
                 <div className="flex gap-3">
                   {/* 下载按钮 */}
                   <button
                     onClick={() => {
                       const filename = `sketch_${previewModal.style}_${Date.now()}.png`;
                       downloadImage(previewModal.imageUrl, filename);
                     }}
                     className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                   >
                     <Download className="w-4 h-4" />
                     {t('actions.download')}
                   </button>
                   {/* 删除按钮 */}
                   <button
                     onClick={() => {
                       setPreviewModal({ isOpen: false, imageUrl: '', resultId: '', style: '' });
                       showDeleteConfirm(previewModal.resultId, previewModal.imageUrl);
                     }}
                     className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors"
                   >
                     <Trash2 className="w-4 h-4" />
                     {t('actions.delete')}
                   </button>
                   {/* 关闭按钮 */}
                   <button
                     onClick={() => setPreviewModal({ isOpen: false, imageUrl: '', resultId: '', style: '' })}
                     className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-400 transition-colors"
                   >
                     {t('actions.close')}
                   </button>
                 </div>
               </div>
            </div>
          </div>
                 </div>
       )}

       {/* 删除确认对话框 */}
       {deleteConfirmModal.isOpen && (
         <div 
           className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
           onClick={() => setDeleteConfirmModal({ isOpen: false, resultId: '', imageUrl: '' })}
         >
           {/* 确认对话框容器 */}
           <div 
             className="relative max-w-md w-full bg-white rounded-xl overflow-hidden shadow-2xl"
             onClick={(e) => e.stopPropagation()}
           >
             {/* 头部 */}
             <div className="p-6 text-center">
               <div className="mx-auto flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                 <Trash2 className="w-8 h-8 text-red-600" />
               </div>
               <h3 className="text-lg font-semibold text-gray-900 mb-2">
                 {t('delete_confirm.title')}
               </h3>
               <p className="text-gray-600 mb-4">
                 {t('delete_confirm.message')}
               </p>
               
               {/* 图片预览 */}
               <div className="w-20 h-20 mx-auto mb-4 rounded-lg overflow-hidden border-2 border-gray-200">
                 <img
                   src={deleteConfirmModal.imageUrl}
                   alt={t('delete_confirm.image_alt')}
                   className="w-full h-full object-cover"
                 />
               </div>
             </div>
             
             {/* 底部按钮 */}
             <div className="flex gap-3 p-6 pt-0">
               <button
                 onClick={() => setDeleteConfirmModal({ isOpen: false, resultId: '', imageUrl: '' })}
                 className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors"
               >
                 {t('actions.cancel')}
               </button>
               <button
                 onClick={() => executeDelete(deleteConfirmModal.resultId)}
                 className="flex-1 px-4 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors"
               >
                 {t('actions.delete')}
               </button>
             </div>
           </div>
         </div>
       )}
     </div>
   );
 } 