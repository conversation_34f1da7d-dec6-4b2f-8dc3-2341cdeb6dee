-- 验证 email_verification_tokens 表结构
-- 如果表不存在，创建它

CREATE TABLE IF NOT EXISTS email_verification_tokens (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 查看表结构
\d email_verification_tokens

-- 清理测试数据（可选）
-- DELETE FROM email_verification_tokens WHERE created_at < NOW() - INTERVAL '1 day'; 