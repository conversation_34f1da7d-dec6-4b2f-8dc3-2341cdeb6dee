import { getUserEmail, getUserUuid } from "@/services/user";
import { insertOrder, updateOrderSession } from "@/models/order";
import { respData, respErr } from "@/lib/resp";

import { Order } from "@/types/order";
import Stripe from "stripe";
import { findUserByUuid } from "@/models/user";
import { getSnowId } from "@/lib/hash";
import { getPricingPage } from "@/services/page";
import { PricingItem } from "@/types/blocks/pricing";
import { orders } from "@/db/schema";
import { createSecurityMiddleware, validateCheckoutRequest, logAuditEvent, getClientIP } from "@/lib/security";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const requestBody = await req.json();
    let {
      credits,
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
    } = requestBody;

    // 🔥 修复URL配置问题
    const getFullUrl = (path: string): string => {
      const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || process.env.VERCEL_URL || 'https://imagetosketch.ai';
      
      // 确保baseUrl有协议
      const formattedBaseUrl = baseUrl.startsWith('http') ? baseUrl : `https://${baseUrl}`;
      
      // 如果path已经是完整URL，直接返回
      if (path.startsWith('http')) {
        return path;
      }
      
      // 确保path以/开头
      const formattedPath = path.startsWith('/') ? path : `/${path}`;
      
      return `${formattedBaseUrl}${formattedPath}`;
    };

    // 设置默认的cancel_url
    if (!cancel_url) {
      cancel_url = getFullUrl(process.env.NEXT_PUBLIC_PAY_CANCEL_URL || '/pricing');
    } else {
      cancel_url = getFullUrl(cancel_url);
    }

    // 验证必需参数
    if (!amount || !interval || !currency || !product_id || !credits) {
      return respErr("invalid params");
    }

    // 验证积分数量的基本合理性
    if (typeof credits !== 'number' || credits <= 0 || credits > 10000) {
      return respErr("invalid credits amount");
    }

    // 检查Stripe密钥
    const stripePrivateKey = process.env.STRIPE_PRIVATE_KEY;
    if (!stripePrivateKey) {
      console.error('STRIPE_PRIVATE_KEY is not configured');
      return respErr("payment service not configured");
    }

    // validate checkout params
    const page = await getPricingPage("en");
    if (!page || !page.pricing || !page.pricing.items) {
      return respErr("invalid pricing table");
    }

    const item = page.pricing.items.find(
      (item: PricingItem) => item.product_id === product_id
    );

    let isPriceValid = false;

    if (currency === "cny") {
      isPriceValid = item?.cn_amount === amount;
    } else {
      isPriceValid = item?.amount === amount && item?.currency === currency;
    }

    if (
      !item ||
      !item.amount ||
      !item.interval ||
      !item.currency ||
      item.interval !== interval ||
      !isPriceValid
    ) {
      return respErr("invalid checkout params");
    }

    // 🔥 新增：验证积分数量与产品配置匹配
    if (!item.credits || item.credits !== credits) {
      return respErr("invalid credits amount for this product");
    }

    if (!["year", "month", "one-time"].includes(interval)) {
      return respErr("invalid interval");
    }

    const is_subscription = interval === "month" || interval === "year";

    if (interval === "year" && valid_months !== 12) {
      return respErr("invalid valid_months");
    }

    if (interval === "month" && valid_months !== 1) {
      return respErr("invalid valid_months");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth, please sign-in");
    }

    // 🔥 新增：安全检查 - 频率限制和请求验证
    const isDevelopment = process.env.NODE_ENV === 'development';
    const securityMiddleware = createSecurityMiddleware({
      rateLimitConfig: {
        maxRequests: isDevelopment ? 50 : 5, // 开发环境更宽松的限制
        windowMs: 60000
      },
      enableAuditLog: true,
      validateRequest: validateCheckoutRequest
    });

    const securityResult = await securityMiddleware(req, user_uuid, 'checkout', requestBody);
    if (!securityResult.allowed) {
      return respErr(securityResult.error || "Security check failed");
    }

    let user_email = await getUserEmail();
    if (!user_email) {
      const user = await findUserByUuid(user_uuid);
      if (user) {
        user_email = user.email;
      }
    }
    if (!user_email) {
      return respErr("invalid user");
    }

    const order_no = getSnowId();
    const currentDate = new Date();
    const created_at = currentDate.toISOString();

    let expired_at = "";
    const timePeriod = new Date(currentDate);
    timePeriod.setMonth(currentDate.getMonth() + valid_months);
    const timePeriodMillis = timePeriod.getTime();
    let delayTimeMillis = 0;

    // subscription
    if (is_subscription) {
      delayTimeMillis = 24 * 60 * 60 * 1000; // delay 24 hours expired
    }

    const newTimeMillis = timePeriodMillis + delayTimeMillis;
    const newDate = new Date(newTimeMillis);
    expired_at = newDate.toISOString();

    const order = {
      order_no: order_no,
      created_at: new Date(created_at),
      user_uuid: user_uuid,
      user_email: user_email,
      amount: amount,
      interval: interval,
      expired_at: new Date(expired_at),
      status: "created",
      credits: credits,
      currency: currency,
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
    };
    
    await insertOrder(order as typeof orders.$inferInsert);

    const stripe = new Stripe(stripePrivateKey);

    let options: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: currency,
            product_data: {
              name: product_name,
            },
            unit_amount: amount,
            recurring: is_subscription
              ? {
                  interval: interval,
                }
              : undefined,
          },
          quantity: 1,
        },
      ],
      allow_promotion_codes: true,
      metadata: {
        project: process.env.NEXT_PUBLIC_PROJECT_NAME || "ImageToSketch",
        product_name: product_name,
        order_no: order_no.toString(),
        user_email: user_email,
        credits: credits.toString(),
        user_uuid: user_uuid,
      },
      mode: is_subscription ? "subscription" : "payment",
      // 🔥 修复URL问题
      success_url: getFullUrl(`/pay-success/{CHECKOUT_SESSION_ID}`),
      cancel_url: cancel_url,
    };

    if (user_email) {
      options.customer_email = user_email;
    }

    if (is_subscription) {
      options.subscription_data = {
        metadata: options.metadata,
      };
    }

    if (currency === "cny") {
      options.payment_method_types = ["wechat_pay", "alipay", "card"];
      options.payment_method_options = {
        wechat_pay: {
          client: "web",
        },
        alipay: {},
      };
    }

    const order_detail = JSON.stringify(options);

    // 🔥 添加更详细的错误处理
    try {
      const session = await stripe.checkout.sessions.create(options);
      const stripe_session_id = session.id;

      await updateOrderSession(order_no, stripe_session_id, order_detail);

      // 🔥 新增：记录成功的支付会话创建
      logAuditEvent({
        user_uuid: user_uuid,
        operation_type: 'checkout_session_created',
        operation_details: {
          order_no: order_no,
          product_id: product_id,
          amount: amount,
          currency: currency,
          credits: credits,
          session_id: stripe_session_id
        },
        credits_amount: credits,
        ip_address: getClientIP(req),
        user_agent: req.headers.get('user-agent') || 'unknown',
        success: true
      });

      return respData({
        public_key: process.env.STRIPE_PUBLIC_KEY,
        order_no: order_no,
        session_id: stripe_session_id,
      });
    } catch (stripeError) {
      console.error('Stripe session creation failed:', stripeError);

      // 安全地获取错误消息
      const errorMessage = stripeError instanceof Error ? stripeError.message : String(stripeError);

      // 🔥 新增：记录支付会话创建失败
      logAuditEvent({
        user_uuid: user_uuid,
        operation_type: 'checkout_session_failed',
        operation_details: {
          order_no: order_no,
          product_id: product_id,
          amount: amount,
          currency: currency,
          credits: credits,
          error: errorMessage
        },
        credits_amount: credits,
        ip_address: getClientIP(req),
        user_agent: req.headers.get('user-agent') || 'unknown',
        success: false,
        error_message: errorMessage
      });

      return respErr(`Payment gateway error: ${errorMessage}`);
    }

  } catch (e: any) {
    console.error("Checkout failed:", e);
    return respErr("checkout failed: " + e.message);
  }
}
