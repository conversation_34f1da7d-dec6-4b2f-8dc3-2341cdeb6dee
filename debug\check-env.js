// 快速检查环境变量
// 运行: node debug/check-env.js

console.log("🔍 环境变量检查\n");

const envVars = {
  'NEXTAUTH_SECRET': process.env.NEXTAUTH_SECRET,
  'NEXTAUTH_URL': process.env.NEXTAUTH_URL,
  'NEXT_PUBLIC_AUTH_EMAIL_ENABLED': process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED,
  'RESEND_API_KEY': process.env.RESEND_API_KEY,
  'NEXT_PUBLIC_EMAIL_FROM': process.env.NEXT_PUBLIC_EMAIL_FROM,
  'DATABASE_URL': process.env.DATABASE_URL,
};

Object.entries(envVars).forEach(([key, value]) => {
  if (key === 'NEXTAUTH_SECRET' || key === 'RESEND_API_KEY' || key === 'DATABASE_URL') {
    console.log(`${value ? '✅' : '❌'} ${key}: ${value ? '已设置' : '未设置'}`);
  } else {
    console.log(`${value ? '✅' : '❌'} ${key}: ${value || '未设置'}`);
  }
});

console.log("\n🎯 关键检查点:");
console.log(`- NEXTAUTH_SECRET: ${process.env.NEXTAUTH_SECRET ? '✅' : '❌'} (JWT token创建必需)`);
console.log(`- NEXTAUTH_URL: ${process.env.NEXTAUTH_URL ? '✅' : '❌'} (重定向URL必需)`);
console.log(`- 邮箱功能: ${process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === 'true' ? '✅' : '❌'} (功能开关)`);

if (!process.env.NEXTAUTH_SECRET) {
  console.log("\n⚠️ 缺少 NEXTAUTH_SECRET - 这可能是验证失败的主要原因！");
}

if (!process.env.NEXTAUTH_URL) {
  console.log("\n⚠️ 缺少 NEXTAUTH_URL - 重定向可能无法正常工作！");
} 