# 邮箱验证手动测试指南

## 问题描述
用户点击邮件中的验证链接后没有成功登录。

## 测试步骤

### 1. 直接访问验证链接
在浏览器地址栏输入：
```
http://localhost:3000/api/auth/email/verify?token=YOUR_TOKEN
```

### 2. 使用测试页面
访问 `http://localhost:3000/zh/test-email-verify` 或 `http://localhost:3000/en/test-email-verify` 并输入token进行测试。

### 3. 检查服务器日志
在开发服务器的控制台中查看以下日志：
- 🔍 Email verification started
- 📧 Token received: [token]
- 📊 Database query result: Found/Not found
- 📋 Token record: {...}
- ⏰ Token expiry check: {...}
- 👤 Creating user info...
- 💾 Saving user to database...
- ✅ User saved successfully: {...}
- 🔑 Creating JWT token...
- 🍪 Setting cookie and redirecting...
- 🎉 Email verification completed successfully

### 4. 检查可能的错误

**Token不存在或已过期：**
- 检查数据库中是否有该token
- 确认token没有过期（15分钟）

**数据库连接问题：**
- 确认数据库连接正常
- 检查表结构是否正确

**环境变量问题：**
- 确认 `NEXTAUTH_SECRET` 已设置
- 确认 `NEXTAUTH_URL` 正确

**JWT创建问题：**
- 检查 `next-auth/jwt` 包是否正确安装
- 确认JWT创建没有错误

### 5. 常见解决方案

1. **重启开发服务器**
2. **清除浏览器缓存**
3. **检查环境变量设置**
4. **确认数据库表结构**

### 6. 调试输出

正常情况下应该看到如下流程：
1. 接收到token
2. 在数据库中找到token
3. 验证token未过期
4. 删除token
5. 创建用户
6. 生成JWT
7. 设置cookie
8. 重定向到首页

如果任何步骤失败，会看到相应的错误信息。 