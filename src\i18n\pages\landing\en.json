{"template": "my-app", "theme": "dark", "header": {"brand": {"title": "ImageToSketch.ai", "logo": {"src": "/logo.png", "alt": "Image To Sketch AI"}, "url": "/"}, "nav": {"items": [{"title": "Sketch To Image", "url": "/sketchtoimage"}, {"title": "Pricing", "url": "/pricing"}, {"title": "My Creations", "url": "/my-creations"}]}, "show_sign": true, "show_theme": false, "show_locale": true}, "imagetosketch": {"name": "imagetosketch", "title": "Image To Sketch AI Converter", "description": "Transform your images or photos into beautiful sketches with AI-powered artistic styles", "disabled": false, "upload": {"title": "Image Upload", "description": "Upload an image to use as a reference, maximum 3 images allowed.", "button": "Upload Image", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "Uploaded Images"}, "style": {"title": "Sketch Style", "description": "Choose your preferred sketch style to transform your image", "options": {"sketcher": {"label": "<PERSON><PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/sketcher.png", "description": "General sketching style with artistic flair"}, "pencil": {"label": "<PERSON><PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/pencil.png", "description": "Classic pencil drawing effect"}, "animelineart": {"label": "Anime Line Art", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/animelineart.png", "description": "Anime Line Art Style"}, "comicnoir": {"label": "Comic Noir", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/comicnoir.png", "description": "Dark comic book noir style"}, "crosshatch": {"label": "Cross Hatch", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/crosshatch.png", "description": "Cross-hatching shading technique"}, "charcoal": {"label": "Charc<PERSON>l", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/charcoal.png", "description": "Deep charcoal drawing style"}, "colorsketch": {"label": "Color Sketch", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/colorsketch.png", "description": "Colorful sketch with vibrant tones"}, "crayon": {"label": "Crayon", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/crayon.png", "description": "Artistic crayon drawing style"}, "penart": {"label": "Pen Art", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/penart.png", "description": "Clean pen line art style"}, "inkwash": {"label": "Ink Was<PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/inkwash.png", "description": "Traditional ink wash painting"}, "graphicnovel": {"label": "Graphic Novel", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/graphicnovel.png", "description": "Modern graphic novel illustration style"}}}, "aspectRatio": {"title": "Aspect Ratio", "description": "Choose the aspect ratio you want to use", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "generate": {"button": "Convert", "generating": "Converting...", "icon": "✨", "button_with_credits": "Convert ({credits} credits)", "insufficient_credits": "Insufficient credits", "insufficient_credits_error": "Insufficient credits, please top up", "need_credits": "Need {credits} credits", "success": "Images generated successfully!", "failed": "Generation failed, please try again", "network_error": "Network error, please try again", "server_busy": "Server is busy, please try again later", "regenerate_success": "Images loaded for regeneration", "regenerate_failed": "Regeneration failed, please try again", "no_images_error": "Please upload at least one image"}, "preview": {"empty": {"title": "Upload an image to get started", "description": "Upload an image and select a sketch style to generate amazing artistic results"}, "navigation": {"of": "of"}, "download": "Download sketch", "transformLabel": "Transformed to sketch style", "modal": {"title": "Image Preview", "close": "Close", "download": "Download", "closeHint": "Click outside or press ESC to close", "downloadFilename": "sketch_{timestamp}.png"}}, "actions": {"add_more": "Add More", "add": "Add", "preview": "Preview", "download": "Download", "retry": "Retry", "regenerate": "Regenerate", "open_image": "Open Image", "close": "Close"}, "status": {"generating": "Generating", "completed": "Completed", "error": "Error"}, "alt_text": {"generated_sketch": "Generated sketch", "uploaded_image": "Uploaded image"}, "tooltips": {"preview_image": "Preview image", "download_image": "Download image", "delete_image": "Delete image"}, "errors": {"failed_to_load": "Failed to load", "download_failed_title": "Download Instructions", "download_failed_message": "Automatic download failed. Please follow these steps:", "download_steps": ["Click \"Open Image\" below", "Right-click on the image", "Select \"Save image as...\"", "Save as \"{filename}\""]}, "messages": {"maxImagesError": "Maximum {max} images allowed", "fileTooLarge": "File {name} is too large. Maximum size is {size}MB", "formatNotSupported": "File {name} format not supported", "uploadSuccess": "{count} image(s) uploaded successfully", "noImages": "Please upload at least one image", "generateSuccess": "Sketches generated successfully!", "generateError": "Failed to generate sketches. Please try again."}, "examples": {"panel_title": "Examples", "recent_tasks_title": "Recent Tasks", "view_all": "View All", "no_tasks": "No tasks yet", "no_tasks_description": "Generate your first sketch to see it here", "loading_history": "Loading history...", "navigation": {"of": "/"}, "items": [{"title": "Anime Line Style", "description": "Transform your photos into beautiful Anime Line style", "url": "https://r2.imagetosketch.ai/sketch-to-image/imagetosketch_example1.png"}], "cta": {"title": "Want to convert your own images to sketches?", "button": "Sign in to get started"}}, "labels": {"style": "Style:", "ratio": "Ratio:"}}, "imagetoimage": {"name": "imagetoimage", "title": "Image to Image AI Generator", "description": "<PERSON><PERSON>ss AI to morph, stylize, and recreate any image effortlessly", "disabled": false, "upload": {"title": "Image Upload", "description": "Upload an image to use as a reference, maximum 5 images allowed.", "button": "Upload Image", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "Uploaded Images"}, "prompt": {"title": "Prompt", "description": "Describe what you want to change in the image", "placeholder": "Example: Change the background to a blue sky.", "maxLength": "1000"}, "aspectRatio": {"title": "Aspect Ratio", "description": "Choose the aspect ratio you want to use", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "outputs": {"title": "Number of Outputs", "description": "Choose the number of outputs you want to generate"}, "generate": {"button": "Generate Images", "generating": "Generating...", "icon": "✨"}, "preview": {"empty": {"title": "Upload an image to get started", "description": "Upload an image and describe the changes you want to see to generate amazing results"}, "navigation": {"of": "of"}, "download": "Download image", "transformLabel": "Transformed into Ghibli style"}, "messages": {"maxImagesError": "Maximum {max} images allowed", "fileTooLarge": "File {name} is too large. Maximum size is {size}MB", "formatNotSupported": "File {name} format not supported", "uploadSuccess": "{count} image(s) uploaded successfully", "noImages": "Please upload at least one image", "noPrompt": "Please enter a prompt", "generateSuccess": "Images generated successfully!", "generateError": "Failed to generate images. Please try again."}}, "introduce": {"name": "introduce", "title": "What is Image to Sketch AI Converter?", "label": "Introduce", "before_image": {"src": "https://r2.imagetosketch.ai/sketch-to-image/before.png", "alt": "Original image before AI transformation"}, "after_image": {"src": "https://r2.imagetosketch.ai/sketch-to-image/after.png", "alt": "Image after AI transformation"}, "content": "Image to Sketch AI Converter is a powerful transformation tool that lets you convert existing images into beautiful sketches using advanced artificial intelligence. Unlike text-to-image generators that create images from scratch, our technology preserves the structure and composition of your original image while applying the changes you describe.\n\nOur system utilizes state-of-the-art neural networks that understand both your source image and your instructions, intelligently applying transformations while maintaining the integrity of key elements. This provides exceptional control over the final result with the perfect balance between transformation and preservation.\n\nWhether you're a designer seeking to visualize concepts, a marketer creating multiple variations of campaign assets, or a creative enthusiast experimenting with artistic styles, Image to Sketch AI Converter delivers professional-quality results with remarkable efficiency."}, "benefit": {"name": "benefit", "title": "Image to Sketch AI Converter: 3 Simple Steps", "label": "Process", "description": "Creating stunning image variations is quick and intuitive:", "items": [{"step": "1", "title": "Upload Your Photo", "description": "Select an image from your device to use as the starting point. For best results, use clear images with good lighting and resolution that match your desired output aspect ratio.", "icon": "RiUploadLine"}, {"step": "2", "title": "Choose Your Style", "description": "Select from various sketch styles including pencil, charcoal, ink, crosshatch, and more. Each style offers unique artistic characteristics to match your vision.", "icon": "RiBrushLine"}, {"step": "3", "title": "Convert & Download", "description": "Click 'Convert' and let the AI do the magic. In a few seconds, download your transformed image in high resolution and use it anywhere.", "icon": "RiDownloadLine"}]}, "feature": {"name": "feature", "title": "Why Choose Our Image to Sketch AI?", "description": "Turn Your Photos into Stunning Sketches in Seconds", "items": [{"title": "Preserve Photo Structure with Precision", "description": "Unlike generic sketch filters or apps, our AI preserves the core structure, proportions, and visual balance of your original image. You get a sketch that remains true to the subject—faces, landscapes, and objects are accurately represented in line with your source photo.", "icon": "🎯"}, {"title": "Instant AI Sketch Generation", "description": "No more waiting around. Our cutting-edge neural networks generate high-quality sketch versions of your images in seconds. Whether you're creating concept art, thumbnails, or client mockups, you'll get results instantly, so you can iterate faster and create more.", "icon": "⚡"}, {"title": "Multiple Sketch Styles to <PERSON><PERSON>", "description": "Choose from a wide range of AI-powered sketch styles—including pencil, charcoal, ink wash, color sketch, fashion sketch, crayon, comic noir, and more. Each style is fine-tuned to produce distinct artistic effects, helping you find the perfect look for your project.", "icon": "🎨"}, {"title": "High-Resolution Output for Professional Use", "description": "Export your sketches in high resolution, perfect for commercial or personal use. Whether it's for social media, presentations, design portfolios, book illustrations, or marketing materials, your AI-generated sketches are print-ready and production-friendly.", "icon": "🖼️"}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What Users Say About Image to Sketch AI", "description": "Discover how real users—from artists to content creators—are turning photos into stunning sketches using our AI-powered tool.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Digital Artist", "description": "Image to Sketch AI helps me quickly turn concept photos into line art. The sketch results feel natural and save me hours of manual drawing.", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "Photography Enthusiast", "description": "I love how the AI preserves the details of my photos while turning them into beautiful pencil-style sketches. It's perfect for printing and framing.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Freelance Designer", "description": "Image to Sketch AI adds an artistic touch to my portfolio. Clients love the sketch look, and I can generate variations in just seconds.", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "Creative Blogger", "description": "I use the sketch versions of my photos as blog visuals. They make my content feel unique and polished without needing design software.", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "University Student", "description": "Used Image to Sketch AI for an art project. The tool gave me clean and detailed results—much better than any mobile app I’ve tried.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Social Media Creator", "description": "It’s the easiest way to create stylized sketch content from selfies and product shots. Great for growing my Instagram page with original visuals.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About Image to Sketch AI", "description": "Need more help? Reach us anytime via live chat or email.", "items": [{"title": "What is Image to Sketch AI and how does it work?", "description": "Image to Sketch AI is an online tool that converts photos into professional-quality sketches using advanced neural networks. Simply upload your picture, choose a sketch style, and our AI generates the result—no design software or technical setup required."}, {"title": "Do I need design skills or text prompts to use it?", "description": "No. The platform is fully point-and-click: upload an image, select one of the available sketch styles, and click Generate. There are no complicated text prompts or editing skills needed."}, {"title": "Which image formats and sizes are supported?", "description": "You can upload JPEG, PNG, GIF, or WEBP files up to 20 MB each. For the sharpest results, use high-resolution photos that match your desired aspect ratio."}, {"title": "How many free conversions do new users receive?", "description": "Every new account starts with 2 free credits—enough to create two sketches at no cost. Additional credits can be purchased or earned through referral bonuses."}, {"title": "How long does it take to generate a sketch?", "description": "Most sketches are ready in under 10 seconds. Complex styles or very large images may take up to a minute."}, {"title": "What sketch styles can I choose from?", "description": "The library includes pencil, charcoal, ink wash, color sketch, comic noir, crayon, and more. New styles are added regularly based on user feedback."}, {"title": "Can I use the generated sketches for commercial projects?", "description": "Yes. All paid plans grant full commercial rights, so you can use your sketches in marketing materials, products, and client work. Free-credit outputs are also royalty-free for personal use."}, {"title": "Are my uploaded images safe and private?", "description": "Absolutely. Uploaded files are processed securely and stored in encrypted Cloudflare R2 buckets. You can delete them at any time from your dashboard."}]}, "cta": {"name": "cta", "title": "Start Converting Your Images Today", "description": "Start by uploading an image and see how AI turns it into stunning sketch art—no design experience needed.", "buttons": [{"title": "Convert an Image Now", "url": "/", "target": "_self", "icon": "GoArrowUpRight"}]}, "footer": {"name": "footer", "brand": {"title": "Image To Sketch AI", "description": "Convert any photo into high-resolution pencil, charcoal, or ink sketches in seconds with our AI-powered Image to Sketch converter—no design skills required.", "logo": {"src": "/logo.png", "alt": "ImageToSketch.ai"}, "url": "/"}, "copyright": "© 2025 • imagetosketch.ai All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Pricing", "url": "/pricing", "target": "_self"}]}, {"title": "AI Image Tools", "children": [{"title": "Image To Sketch", "url": "/", "target": "_self"}, {"title": "Sketch To Image", "url": "/sketchtoimage", "target": "_self"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}, {"title": "Refund Policy", "url": "/refund-policy"}]}}}