import { NextRequest } from 'next/server';

// 请求频率限制
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

// 清理过期的限制记录
function cleanupExpiredEntries() {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// 定期清理过期记录（每5分钟）
setInterval(cleanupExpiredEntries, 5 * 60 * 1000);

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
}

export function checkRateLimit(
  identifier: string, 
  config: RateLimitConfig = { maxRequests: 10, windowMs: 60000 }
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const entry = rateLimitStore.get(identifier);

  if (!entry || now > entry.resetTime) {
    // 新的时间窗口或首次请求
    const newEntry: RateLimitEntry = {
      count: 1,
      resetTime: now + config.windowMs
    };
    rateLimitStore.set(identifier, newEntry);
    
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: newEntry.resetTime
    };
  }

  if (entry.count >= config.maxRequests) {
    // 超过限制
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime
    };
  }

  // 增加计数
  entry.count++;
  rateLimitStore.set(identifier, entry);

  return {
    allowed: true,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime
  };
}

// 审计日志接口
export interface AuditLogEntry {
  user_uuid: string;
  operation_type: string;
  operation_details: any;
  credits_amount?: number;
  ip_address?: string;
  user_agent?: string;
  timestamp: Date;
  success: boolean;
  error_message?: string;
}

// 简单的内存审计日志存储（生产环境应该使用数据库）
const auditLogs: AuditLogEntry[] = [];
const MAX_AUDIT_LOGS = 10000; // 最多保存10000条记录

export function logAuditEvent(entry: Omit<AuditLogEntry, 'timestamp'>) {
  // 异步执行审计日志，避免阻塞主业务逻辑
  setImmediate(() => {
    try {
      const auditEntry: AuditLogEntry = {
        ...entry,
        timestamp: new Date()
      };

      auditLogs.push(auditEntry);

      // 保持日志数量在限制内
      if (auditLogs.length > MAX_AUDIT_LOGS) {
        auditLogs.splice(0, auditLogs.length - MAX_AUDIT_LOGS);
      }

      // 在生产环境中，这里应该写入数据库
      console.log('Audit Log:', JSON.stringify(auditEntry));
    } catch (error) {
      console.error('Failed to log audit event:', error);
    }
  });
}

// 获取客户端IP地址
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (remoteAddr) {
    return remoteAddr;
  }
  
  return 'unknown';
}

// 参数验证白名单
export const VALIDATION_RULES = {
  // ImageToSketch 样式
  ALLOWED_SKETCH_STYLES: [
    'pencil', 'animelineart', 'charcoal', 'ink', 'hatching', 'stippling',
    'contour', 'gesture', 'scribble', 'fashion', 'architectural', 'sketcher',
    'comicnoir', 'crosshatch', 'colorsketch', 'crayon', 'penart', 'inkwash', 'graphicnovel'
  ],
  // SketchToImage 样式
  ALLOWED_SKETCH_TO_IMAGE_STYLES: [
    'architecture', 'default', 'photorealistic', 'digitalart', 'anime', 'interiordesign',
    '3d', 'pixar', 'fantasy', 'rpg', 'comicbook', 'clay', 'vectorart', 'minimalist',
    'watercolor', 'oilpainting', 'gta', 'minecraft'
  ],
  ALLOWED_ASPECTS: ['1:1', '3:2', '2:3'],
  MAX_CREDITS_PER_REQUEST: 50,
  MAX_IMAGES_PER_REQUEST: 10,
  MIN_CREDITS_PER_REQUEST: 1,
  ALLOWED_INTERVALS: ['month', 'year', 'one-time'],
  MAX_CREDITS_PER_PRODUCT: 10000,
  MIN_AMOUNT_CENTS: 99, // 最小金额 $0.99
  MAX_AMOUNT_CENTS: 999999, // 最大金额 $9999.99
};

export function validateImageToSketchRequest(body: any): { valid: boolean; error?: string } {
  // 验证图片数组
  if (!Array.isArray(body.images) || body.images.length === 0) {
    return { valid: false, error: 'Images array is required and must not be empty' };
  }

  if (body.images.length > VALIDATION_RULES.MAX_IMAGES_PER_REQUEST) {
    return { valid: false, error: `Maximum ${VALIDATION_RULES.MAX_IMAGES_PER_REQUEST} images allowed` };
  }

  // 验证风格
  if (!body.style || !VALIDATION_RULES.ALLOWED_SKETCH_STYLES.includes(body.style)) {
    return { valid: false, error: `Invalid style. Allowed: ${VALIDATION_RULES.ALLOWED_SKETCH_STYLES.join(', ')}` };
  }

  // 验证宽高比
  if (!body.aspectRatio || !VALIDATION_RULES.ALLOWED_ASPECTS.includes(body.aspectRatio)) {
    return { valid: false, error: `Invalid aspect ratio. Allowed: ${VALIDATION_RULES.ALLOWED_ASPECTS.join(', ')}` };
  }

  // 验证积分需求
  if (body.creditsNeeded !== undefined) {
    if (typeof body.creditsNeeded !== 'number' ||
        body.creditsNeeded < VALIDATION_RULES.MIN_CREDITS_PER_REQUEST ||
        body.creditsNeeded > VALIDATION_RULES.MAX_CREDITS_PER_REQUEST) {
      return {
        valid: false,
        error: `Invalid credits amount. Must be between ${VALIDATION_RULES.MIN_CREDITS_PER_REQUEST} and ${VALIDATION_RULES.MAX_CREDITS_PER_REQUEST}`
      };
    }

    // 确保积分需求与图片数量合理匹配
    if (body.creditsNeeded < body.images.length || body.creditsNeeded > body.images.length * 5) {
      return { valid: false, error: 'Credits needed must be reasonable for the number of images' };
    }
  }

  return { valid: true };
}

export function validateSketchToImageRequest(body: any): { valid: boolean; error?: string } {
  // 验证图片数组（支持两种格式）
  if (!Array.isArray(body.images) || body.images.length === 0) {
    return { valid: false, error: 'Images array is required and must not be empty' };
  }

  if (body.images.length > VALIDATION_RULES.MAX_IMAGES_PER_REQUEST) {
    return { valid: false, error: `Maximum ${VALIDATION_RULES.MAX_IMAGES_PER_REQUEST} images allowed` };
  }

  // 验证风格
  if (!body.style || !VALIDATION_RULES.ALLOWED_SKETCH_TO_IMAGE_STYLES.includes(body.style)) {
    return { valid: false, error: `Invalid style. Allowed: ${VALIDATION_RULES.ALLOWED_SKETCH_TO_IMAGE_STYLES.join(', ')}` };
  }

  // 验证宽高比
  if (!body.aspectRatio || !VALIDATION_RULES.ALLOWED_ASPECTS.includes(body.aspectRatio)) {
    return { valid: false, error: `Invalid aspect ratio. Allowed: ${VALIDATION_RULES.ALLOWED_ASPECTS.join(', ')}` };
  }

  // 验证积分需求
  if (body.creditsNeeded !== undefined) {
    if (typeof body.creditsNeeded !== 'number' ||
        body.creditsNeeded < VALIDATION_RULES.MIN_CREDITS_PER_REQUEST ||
        body.creditsNeeded > VALIDATION_RULES.MAX_CREDITS_PER_REQUEST) {
      return {
        valid: false,
        error: `Invalid credits amount. Must be between ${VALIDATION_RULES.MIN_CREDITS_PER_REQUEST} and ${VALIDATION_RULES.MAX_CREDITS_PER_REQUEST}`
      };
    }

    // 确保积分需求与图片数量合理匹配
    const imageCount = body.images.length;
    if (body.creditsNeeded < imageCount || body.creditsNeeded > imageCount * 5) {
      return { valid: false, error: 'Credits needed must be reasonable for the number of images' };
    }
  }

  // 验证创意度参数（如果提供）
  if (body.creativity !== undefined) {
    if (typeof body.creativity !== 'number' || body.creativity < 0 || body.creativity > 100) {
      return { valid: false, error: 'Creativity must be a number between 0 and 100' };
    }
  }

  return { valid: true };
}

// 保持向后兼容性
export const validateImageGenRequest = validateImageToSketchRequest;

export function validateCheckoutRequest(body: any): { valid: boolean; error?: string } {
  // 验证必需参数
  const requiredFields = ['amount', 'interval', 'currency', 'product_id', 'credits'];
  for (const field of requiredFields) {
    if (!body[field]) {
      return { valid: false, error: `Missing required field: ${field}` };
    }
  }

  // 验证金额
  if (typeof body.amount !== 'number' || 
      body.amount < VALIDATION_RULES.MIN_AMOUNT_CENTS || 
      body.amount > VALIDATION_RULES.MAX_AMOUNT_CENTS) {
    return { 
      valid: false, 
      error: `Invalid amount. Must be between ${VALIDATION_RULES.MIN_AMOUNT_CENTS} and ${VALIDATION_RULES.MAX_AMOUNT_CENTS} cents` 
    };
  }

  // 验证间隔
  if (!VALIDATION_RULES.ALLOWED_INTERVALS.includes(body.interval)) {
    return { valid: false, error: `Invalid interval. Allowed: ${VALIDATION_RULES.ALLOWED_INTERVALS.join(', ')}` };
  }

  // 验证积分数量
  if (typeof body.credits !== 'number' || 
      body.credits <= 0 || 
      body.credits > VALIDATION_RULES.MAX_CREDITS_PER_PRODUCT) {
    return { 
      valid: false, 
      error: `Invalid credits amount. Must be between 1 and ${VALIDATION_RULES.MAX_CREDITS_PER_PRODUCT}` 
    };
  }

  // 验证货币
  const allowedCurrencies = ['USD', 'CNY', 'EUR', 'GBP'];
  if (!allowedCurrencies.includes(body.currency.toUpperCase())) {
    return { valid: false, error: `Invalid currency. Allowed: ${allowedCurrencies.join(', ')}` };
  }

  return { valid: true };
}

// 安全中间件函数
export function createSecurityMiddleware(config: {
  rateLimitConfig?: RateLimitConfig;
  enableAuditLog?: boolean;
  validateRequest?: (body: any) => { valid: boolean; error?: string };
}) {
  return async function securityMiddleware(
    request: NextRequest,
    userUuid: string,
    operationType: string,
    requestBody?: any
  ): Promise<{ allowed: boolean; error?: string; headers?: Record<string, string> }> {
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 1. 频率限制检查
    if (config.rateLimitConfig) {
      const rateLimitResult = checkRateLimit(
        `${userUuid}:${operationType}`, 
        config.rateLimitConfig
      );

      if (!rateLimitResult.allowed) {
        // 记录频率限制违规
        if (config.enableAuditLog) {
          logAuditEvent({
            user_uuid: userUuid,
            operation_type: `${operationType}_rate_limited`,
            operation_details: { ip, userAgent },
            ip_address: ip,
            user_agent: userAgent,
            success: false,
            error_message: 'Rate limit exceeded'
          });
        }

        return {
          allowed: false,
          error: 'Rate limit exceeded. Please try again later.',
          headers: {
            'X-RateLimit-Limit': config.rateLimitConfig.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
          }
        };
      }
    }

    // 2. 请求验证
    if (config.validateRequest && requestBody) {
      const validationResult = config.validateRequest(requestBody);
      if (!validationResult.valid) {
        // 记录验证失败
        if (config.enableAuditLog) {
          logAuditEvent({
            user_uuid: userUuid,
            operation_type: `${operationType}_validation_failed`,
            operation_details: { error: validationResult.error, body: requestBody },
            ip_address: ip,
            user_agent: userAgent,
            success: false,
            error_message: validationResult.error
          });
        }

        return {
          allowed: false,
          error: validationResult.error
        };
      }
    }

    // 3. 记录成功的安全检查
    if (config.enableAuditLog) {
      logAuditEvent({
        user_uuid: userUuid,
        operation_type: `${operationType}_security_check`,
        operation_details: { ip, userAgent },
        ip_address: ip,
        user_agent: userAgent,
        success: true
      });
    }

    return { allowed: true };
  };
}
