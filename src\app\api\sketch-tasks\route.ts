import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getSketchTasksWithResultsOptimized, deleteSketchTask } from '@/models/sketch';

export async function GET(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid;

    if (!userUuid) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const taskType = searchParams.get('type') || 'image-to-sketch'; // 添加任务类型筛选

    console.log('Fetching sketch tasks for user:', userUuid, 'with optimized query', 'type:', taskType);

    // 使用优化后的查询获取用户的历史任务和结果
    const tasksWithResults = await getSketchTasksWithResultsOptimized(userUuid, limit, taskType);

    // 格式化URL，确保包含https://前缀
    const formatUrl = (url: string | null): string => {
      if (!url) return '';
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      return `https://${url}`;
    };

    // 转换数据格式，匹配前端期望的结构，并过滤掉空任务
    const formattedTasks = tasksWithResults
      .map(({ task, results }) => ({
        id: task.uuid,
        timestamp: task.created_at,
        status: task.status as 'generating' | 'completed' | 'error',
        style: task.style,
        aspectRatio: task.aspect_ratio,
        originalImages: [],  // 原图信息可以从results中提取
        results: results.map(result => ({
          id: result.uuid,
          originalImageId: result.uuid, // 使用result的uuid作为originalImageId
          url: formatUrl(result.result_image_url),
          original_image_url: formatUrl(result.original_image_url),
          status: result.status as 'generating' | 'completed' | 'error',
          progress: result.status === 'completed' ? 100 : result.status === 'error' ? 0 : 50,
        })),
        originalImageCount: task.original_image_count,
        completedCount: task.completed_count,
        errorMessage: task.error_message,
        isHistory: true,
        isLocal: false,
      }))
      .filter(task => {
        // 过滤掉没有有效结果的任务
        if (!task.results || task.results.length === 0) {
          console.log('API: Filtering out task with no results:', task.id);
          return false;
        }
        
        // 过滤掉所有结果都没有有效URL的任务
        const hasValidResults = task.results.some(result => 
          result.url && result.url.trim() !== '' && result.status === 'completed'
        );
        
        if (!hasValidResults) {
          console.log('API: Filtering out task with no valid results:', task.id);
          return false;
        }
        
        return true;
      });

    console.log('API: Tasks before filtering:', tasksWithResults.length, 'after filtering:', formattedTasks.length);

    console.log('Optimized query returned tasks:', {
      count: formattedTasks.length,
      userUuid,
      queryTime: 'optimized JOIN query'
    });

    return NextResponse.json({
      success: true,
      data: {
        tasks: formattedTasks,
        total: formattedTasks.length
      }
    });

  } catch (error) {
    console.error('Error fetching sketch tasks:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// 删除任务的接口
export async function DELETE(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid;

    if (!userUuid) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('id');

    if (!taskId) {
      return NextResponse.json({
        success: false,
        error: 'Task ID is required'
      }, { status: 400 });
    }

    // 删除任务（这会删除任务及其相关的结果）
    const success = await deleteSketchTask(taskId, userUuid);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: 'Failed to delete task'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Task deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting sketch task:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
} 