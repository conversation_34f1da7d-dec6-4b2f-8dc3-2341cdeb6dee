import { sketchTasks, sketchResults } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and } from "drizzle-orm";
import { sql } from "drizzle-orm";

// Sketch Task 相关操作
export async function insertSketchTask(
  data: typeof sketchTasks.$inferInsert
): Promise<typeof sketchTasks.$inferSelect | undefined> {
  const [task] = await db().insert(sketchTasks).values(data).returning();
  return task;
}

export async function findSketchTaskByUuid(
  uuid: string
): Promise<typeof sketchTasks.$inferSelect | undefined> {
  const [task] = await db()
    .select()
    .from(sketchTasks)
    .where(eq(sketchTasks.uuid, uuid))
    .limit(1);
  return task;
}

export async function updateSketchTaskStatus(
  uuid: string,
  status: string,
  completedCount?: number,
  errorMessage?: string
): Promise<typeof sketchTasks.$inferSelect | undefined> {
  const updateData: any = {
    status,
    updated_at: new Date(),
  };
  
  if (completedCount !== undefined) {
    updateData.completed_count = completedCount;
  }
  
  if (errorMessage !== undefined) {
    updateData.error_message = errorMessage;
  }

  const [task] = await db()
    .update(sketchTasks)
    .set(updateData)
    .where(eq(sketchTasks.uuid, uuid))
    .returning();
  return task;
}

export async function getSketchTasksByUser(
  userUuid: string,
  limit: number = 10
): Promise<(typeof sketchTasks.$inferSelect)[] | undefined> {
  const tasks = await db()
    .select()
    .from(sketchTasks)
    .where(eq(sketchTasks.user_uuid, userUuid))
    .orderBy(desc(sketchTasks.created_at))
    .limit(limit);
  return tasks;
}

// 专门用于获取已完成的任务
export async function getCompletedSketchTasksByUser(
  userUuid: string,
  limit: number = 10
): Promise<(typeof sketchTasks.$inferSelect)[] | undefined> {
  const tasks = await db()
    .select()
    .from(sketchTasks)
    .where(and(
      eq(sketchTasks.user_uuid, userUuid),
      eq(sketchTasks.status, 'completed') // 只查询已完成的任务
    ))
    .orderBy(desc(sketchTasks.created_at))
    .limit(limit);
  return tasks;
}

// Sketch Result 相关操作
export async function insertSketchResult(
  data: typeof sketchResults.$inferInsert
): Promise<typeof sketchResults.$inferSelect | undefined> {
  const [result] = await db().insert(sketchResults).values(data).returning();
  return result;
}

export async function findSketchResultsByTaskUuid(
  taskUuid: string
): Promise<(typeof sketchResults.$inferSelect)[] | undefined> {
  const results = await db()
    .select()
    .from(sketchResults)
    .where(eq(sketchResults.task_uuid, taskUuid))
    .orderBy(desc(sketchResults.created_at));
  return results;
}

export async function updateSketchResultStatus(
  uuid: string,
  status: string,
  resultImageUrl?: string,
  processingTime?: number
): Promise<typeof sketchResults.$inferSelect | undefined> {
  const updateData: any = {
    status,
  };
  
  if (resultImageUrl) {
    updateData.result_image_url = resultImageUrl;
  }
  
  if (processingTime !== undefined) {
    updateData.processing_time = processingTime;
  }

  const [result] = await db()
    .update(sketchResults)
    .set(updateData)
    .where(eq(sketchResults.uuid, uuid))
    .returning();
  return result;
}

// 批量插入结果
export async function insertSketchResults(
  results: (typeof sketchResults.$inferInsert)[]
): Promise<(typeof sketchResults.$inferSelect)[]> {
  const insertedResults = await db()
    .insert(sketchResults)
    .values(results)
    .returning();
  return insertedResults;
}

// 获取用户的历史任务和结果（联合查询）
export async function getSketchTasksWithResults(
  userUuid: string,
  limit: number = 10
): Promise<{
  task: typeof sketchTasks.$inferSelect;
  results: (typeof sketchResults.$inferSelect)[];
}[]> {
  // 先获取已完成的任务
  const tasks = await getCompletedSketchTasksByUser(userUuid, limit);
  if (!tasks || tasks.length === 0) {
    return [];
  }

  // 获取所有结果
  const tasksWithResults: {
    task: typeof sketchTasks.$inferSelect;
    results: (typeof sketchResults.$inferSelect)[];
  }[] = [];

  for (const task of tasks) {
    const results = await findSketchResultsByTaskUuid(task.uuid);
    tasksWithResults.push({
      task,
      results: results || [],
    });
  }

  return tasksWithResults;
}

// 删除任务及其相关结果
export async function deleteSketchTask(
  uuid: string,
  userUuid: string
): Promise<boolean> {
  try {
    // 先删除相关的结果
    await db()
      .delete(sketchResults)
      .where(eq(sketchResults.task_uuid, uuid));

    // 再删除任务（确保是用户自己的任务）
    await db()
      .delete(sketchTasks)
      .where(and(
        eq(sketchTasks.uuid, uuid),
        eq(sketchTasks.user_uuid, userUuid)
      ));

    return true;
  } catch (error) {
    console.error('Error deleting sketch task:', error);
    return false;
  }
} 

// 获取用户所有创作（支持分页和筛选）
export async function getUserCreations(
  userUuid: string,
  options: {
    page?: number;
    limit?: number;
    style?: string;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
): Promise<{
  creations: Array<{
    task: typeof sketchTasks.$inferSelect;
    results: (typeof sketchResults.$inferSelect)[];
  }>;
  total: number;
  hasMore: boolean;
}> {
  const { page = 1, limit = 20, style, status, dateFrom, dateTo } = options;
  const offset = (page - 1) * limit;

  // 构建查询条件
  let whereConditions = [eq(sketchTasks.user_uuid, userUuid)];
  
  if (style) {
    whereConditions.push(eq(sketchTasks.style, style));
  }
  
  if (status) {
    whereConditions.push(eq(sketchTasks.status, status));
  }
  
  // 获取任务列表（带分页）
  const tasks = await db()
    .select()
    .from(sketchTasks)
    .where(and(...whereConditions))
    .orderBy(desc(sketchTasks.created_at))
    .limit(limit + 1) // 多查询一条来判断是否还有更多
    .offset(offset);

  const hasMore = tasks.length > limit;
  const actualTasks = hasMore ? tasks.slice(0, limit) : tasks;

  // 获取总数
  const totalResult = await db()
    .select({ count: sql<number>`count(*)` })
    .from(sketchTasks)
    .where(and(...whereConditions));
  
  const total = totalResult[0]?.count || 0;

  // 获取每个任务的结果
  const creations = [];
  for (const task of actualTasks) {
    const results = await findSketchResultsByTaskUuid(task.uuid);
    creations.push({
      task,
      results: results || [],
    });
  }

    return {
    creations,
    total,
    hasMore,
  };
}

// 删除图片结果
export async function deleteSketchResult(
  resultUuid: string,
  userUuid: string
): Promise<boolean> {
  try {
    // 首先验证该结果是否属于该用户
    const result = await db()
      .select({
        id: sketchResults.id,
        taskUuid: sketchResults.task_uuid,
      })
      .from(sketchResults)
      .innerJoin(sketchTasks, eq(sketchResults.task_uuid, sketchTasks.uuid))
      .where(and(
        eq(sketchResults.uuid, resultUuid),
        eq(sketchTasks.user_uuid, userUuid)
      ))
      .limit(1);

    if (result.length === 0) {
      return false; // 图片不存在或不属于该用户
    }

    // 删除图片结果
    await db()
      .delete(sketchResults)
      .where(eq(sketchResults.uuid, resultUuid));

    return true;
  } catch (error) {
    console.error('Error deleting sketch result:', error);
    return false;
  }
}

// 增强版：删除图片结果，如果任务没有其他结果则删除整个任务
export async function deleteSketchResultWithTaskCleanup(
  resultUuid: string,
  userUuid: string
): Promise<{
  success: boolean;
  taskDeleted: boolean;
  taskId?: string;
  taskType?: string;
}> {
  try {
    // 首先验证该结果是否属于该用户，并获取任务信息
    const result = await db()
      .select({
        id: sketchResults.id,
        taskUuid: sketchResults.task_uuid,
        taskType: sketchTasks.task_type,
      })
      .from(sketchResults)
      .innerJoin(sketchTasks, eq(sketchResults.task_uuid, sketchTasks.uuid))
      .where(and(
        eq(sketchResults.uuid, resultUuid),
        eq(sketchTasks.user_uuid, userUuid)
      ))
      .limit(1);

    if (result.length === 0) {
      return { success: false, taskDeleted: false }; // 图片不存在或不属于该用户
    }

    const taskUuid = result[0].taskUuid;
    const taskType = result[0].taskType ?? undefined;

    // 删除图片结果
    await db()
      .delete(sketchResults)
      .where(eq(sketchResults.uuid, resultUuid));

    // 检查该任务是否还有其他结果
    const remainingResults = await db()
      .select({ count: sql<number>`count(*)` })
      .from(sketchResults)
      .where(eq(sketchResults.task_uuid, taskUuid));

    const remainingCount = remainingResults[0]?.count || 0;

    if (remainingCount === 0) {
      // 如果没有其他结果，删除整个任务
      // 注意：由于外键关系，删除任务时应该先删除所有相关的结果
      // 但由于我们已经删除了指定的结果，现在只需要删除任务即可
      await db()
        .delete(sketchTasks)
        .where(and(
          eq(sketchTasks.uuid, taskUuid),
          eq(sketchTasks.user_uuid, userUuid)
        ));
      
      return { success: true, taskDeleted: true, taskId: taskUuid, taskType };
    }

    return { success: true, taskDeleted: false, taskType };
  } catch (error) {
    console.error('Error deleting sketch result with cleanup:', error);
    return { success: false, taskDeleted: false };
  }
} 

// ========== 性能优化版本 ==========
// 解决N+1查询问题，使用JOIN一次性获取任务和结果

// 优化版：获取用户的历史任务和结果（使用JOIN查询）
export async function getSketchTasksWithResultsOptimized(
  userUuid: string,
  limit: number = 10,
  taskType: string = 'image-to-sketch'
): Promise<{
  task: typeof sketchTasks.$inferSelect;
  results: (typeof sketchResults.$inferSelect)[];
}[]> {
  console.log('Using optimized query for sketch tasks with results');
  
  // 使用LEFT JOIN一次性获取任务和结果
  const tasksWithResults = await db()
    .select({
      // 任务字段
      task_uuid: sketchTasks.uuid,
      task_user_uuid: sketchTasks.user_uuid,
      task_style: sketchTasks.style,
      task_aspect_ratio: sketchTasks.aspect_ratio,
      task_status: sketchTasks.status,
      task_original_image_count: sketchTasks.original_image_count,
      task_completed_count: sketchTasks.completed_count,
      task_error_message: sketchTasks.error_message,
      task_created_at: sketchTasks.created_at,
      task_updated_at: sketchTasks.updated_at,
      
      // 结果字段
      result_uuid: sketchResults.uuid,
      result_task_uuid: sketchResults.task_uuid,
      result_status: sketchResults.status,
      result_image_url: sketchResults.result_image_url,
      result_original_image_url: sketchResults.original_image_url,
      result_processing_time: sketchResults.processing_time,
      result_created_at: sketchResults.created_at,
    })
    .from(sketchTasks)
    .leftJoin(sketchResults, eq(sketchTasks.uuid, sketchResults.task_uuid))
    .where(and(
      eq(sketchTasks.user_uuid, userUuid),
      eq(sketchTasks.status, 'completed'),
      eq(sketchTasks.task_type, taskType)
    ))
    .orderBy(desc(sketchTasks.created_at))
    .limit(limit * 10); // 预估每个任务最多10个结果，防止截断

  // 组织数据结构
  const taskMap = new Map();
  
  tasksWithResults.forEach(row => {
    const taskId = row.task_uuid;
    
    // 创建任务对象
    if (!taskMap.has(taskId)) {
      taskMap.set(taskId, {
        task: {
          uuid: row.task_uuid,
          user_uuid: row.task_user_uuid,
          style: row.task_style,
          aspect_ratio: row.task_aspect_ratio,
          status: row.task_status,
          original_image_count: row.task_original_image_count,
          completed_count: row.task_completed_count,
          error_message: row.task_error_message,
          created_at: row.task_created_at,
          updated_at: row.task_updated_at,
        },
        results: []
      });
    }
    
    // 添加结果（如果存在）
    if (row.result_uuid) {
      taskMap.get(taskId).results.push({
        uuid: row.result_uuid,
        task_uuid: row.result_task_uuid,
        status: row.result_status,
        result_image_url: row.result_image_url,
        original_image_url: row.result_original_image_url || undefined,
        processing_time: row.result_processing_time,
        created_at: row.result_created_at,
      });
    }
  });

  // 转换为数组并限制数量
  const result = Array.from(taskMap.values()).slice(0, limit);
  console.log(`Optimized query returned ${result.length} tasks with results`);
  return result;
}

// 优化版：获取用户所有创作（使用JOIN查询）
export async function getUserCreationsOptimized(
  userUuid: string,
  options: {
    page?: number;
    limit?: number;
    style?: string;
    status?: string;
    taskType?: string;
  } = {}
): Promise<{
  creations: Array<{
    task: typeof sketchTasks.$inferSelect;
    results: (typeof sketchResults.$inferSelect)[];
  }>;
  total: number;
  hasMore: boolean;
}> {
  const { page = 1, limit = 20, style, status, taskType } = options;
  const offset = (page - 1) * limit;

  console.log('Using optimized query for user creations', { userUuid, page, limit, style, status, taskType });

  // 构建查询条件
  let whereConditions = [eq(sketchTasks.user_uuid, userUuid)];
  
  if (style) {
    whereConditions.push(eq(sketchTasks.style, style));
  }
  
  if (status) {
    whereConditions.push(eq(sketchTasks.status, status));
  }

  if (taskType) {
    whereConditions.push(eq(sketchTasks.task_type, taskType));
  }

  // 获取总数（单独查询，这是必要的）
  const totalResult = await db()
    .select({ count: sql<number>`count(*)` })
    .from(sketchTasks)
    .where(and(...whereConditions));
  
  const total = totalResult[0]?.count || 0;

  // 使用JOIN查询获取任务和结果
  const tasksWithResults = await db()
    .select({
      // 任务字段
      task_uuid: sketchTasks.uuid,
      task_user_uuid: sketchTasks.user_uuid,
      task_style: sketchTasks.style,
      task_aspect_ratio: sketchTasks.aspect_ratio,
      task_status: sketchTasks.status,
      task_original_image_count: sketchTasks.original_image_count,
      task_completed_count: sketchTasks.completed_count,
      task_error_message: sketchTasks.error_message,
      task_created_at: sketchTasks.created_at,
      task_updated_at: sketchTasks.updated_at,
      
             // 结果字段
       result_uuid: sketchResults.uuid,
       result_task_uuid: sketchResults.task_uuid,
       result_status: sketchResults.status,
       result_image_url: sketchResults.result_image_url,
       result_original_image_url: sketchResults.original_image_url,
       result_processing_time: sketchResults.processing_time,
       result_created_at: sketchResults.created_at,
     })
     .from(sketchTasks)
     .leftJoin(sketchResults, eq(sketchTasks.uuid, sketchResults.task_uuid))
     .where(and(...whereConditions))
    .orderBy(desc(sketchTasks.created_at))
    .limit((limit + 1) * 15) // 多查一页判断hasMore，预估每个任务平均15个结果
    .offset(offset * 15);

  // 组织数据结构
  const taskMap = new Map();
  
  tasksWithResults.forEach(row => {
    const taskId = row.task_uuid;
    
    if (!taskMap.has(taskId)) {
      taskMap.set(taskId, {
        task: {
          uuid: row.task_uuid,
          user_uuid: row.task_user_uuid,
          style: row.task_style,
          aspect_ratio: row.task_aspect_ratio,
          status: row.task_status,
          original_image_count: row.task_original_image_count,
          completed_count: row.task_completed_count,
          error_message: row.task_error_message,
          created_at: row.task_created_at,
          updated_at: row.task_updated_at,
        },
        results: []
      });
    }
    
    if (row.result_uuid) {
      taskMap.get(taskId).results.push({
        uuid: row.result_uuid,
        task_uuid: row.result_task_uuid,
        status: row.result_status,
        result_image_url: row.result_image_url,
        original_image_url: row.result_original_image_url || undefined,
        processing_time: row.result_processing_time,
        created_at: row.result_created_at,
      });
    }
  });

  const creations = Array.from(taskMap.values());
  const hasMore = creations.length > limit;
  const actualCreations = hasMore ? creations.slice(0, limit) : creations;

  console.log(`Optimized query returned ${actualCreations.length} creations, total: ${total}, hasMore: ${hasMore}`);

  return {
    creations: actualCreations,
    total,
    hasMore,
  };
} 

 