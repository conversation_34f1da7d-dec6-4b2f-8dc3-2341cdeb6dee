{"metadata": {"title": "Convertidor IA de Imagen a Boceto | Transforma fotos en arte dibujado a mano | ImageToSketch.ai", "description": "Herramienta profesional de IA para convertir fotos en hermosos efectos dibujados a mano. Soporta lápiz, carboncillo, tinta y otros estilos artísticos, alta resolución, licencia comercial. ¡Prueba gratis ahora!", "keywords": "convertir imagen a boceto, foto a dibujo, generador IA bocetos, imagen a arte lineal, herramienta boceto online, foto artística, creación dibujos, arte IA"}, "user": {"sign_in": "In<PERSON><PERSON>", "sign_out": "<PERSON><PERSON><PERSON>", "credits": "C<PERSON>dit<PERSON>", "my_creations": "Mis creaciones", "api_keys": "Claves API", "my_orders": "<PERSON>s pedidos", "user_center": "Centro de usuario", "admin_system": "Sistema de administración"}, "sign_modal": {"sign_in_title": "Por favor inicia sesión para continuar", "sign_in_description": "Te enviaremos un enlace mágico por email", "sign_up_title": "<PERSON><PERSON><PERSON> cuenta", "sign_up_description": "Crear nueva cuenta", "email_title": "Dirección de email", "email_placeholder": "Ingresa tu email", "password_title": "Contraseña", "password_placeholder": "Ingresa tu contraseña", "forgot_password": "¿Olvidaste tu contraseña?", "or": "O inicia sesión con", "continue": "<PERSON><PERSON><PERSON><PERSON>", "no_account": "¿No tienes cuenta?", "email_sign_in": "Iniciar se<PERSON><PERSON> con email", "google_sign_in": "Google", "github_sign_in": "Iniciar se<PERSON><PERSON> con GitHub", "close_title": "<PERSON><PERSON><PERSON>", "cancel_title": "<PERSON><PERSON><PERSON>", "send_magic_link": "<PERSON><PERSON><PERSON> enlace", "sending": "Enviando...", "magic_link_sent": "¡Enlace mágico enviado! Revisa tu email.", "magic_link_error": "Error al enviar. Por favor intenta de nuevo.", "invalid_email": "Por favor ingresa una dirección de email válida", "something_wrong": "Algo salió mal. Por favor intenta de nuevo.", "check_email_title": "Revisa tu email", "check_email_description": "Hemos enviado un enlace de inicio de sesión a tu email. Haz clic en el enlace para iniciar sesión."}, "my_orders": {"title": "<PERSON>s pedidos", "description": "Pedidos pagados con ShipAny.", "no_orders": "No se encontraron pedidos", "tip": "", "activate_order": "Activar pedido", "actived": "Activado", "join_discord": "Unirse a Discord", "read_docs": "Leer documentación", "table": {"order_no": "Número de pedido", "email": "Email", "product_name": "Nombre del producto", "amount": "Cantidad", "paid_at": "Pagado el", "github_username": "Nombre de usuario GitHub", "status": "Estado"}}, "credits": {"remaining": ""}, "my_credits": {"title": "<PERSON><PERSON> c<PERSON><PERSON>", "left_tip": "Cré<PERSON><PERSON> restantes: {left_credits}", "no_credits": "No hay registros de créditos", "recharge": "Recargar", "table": {"trans_no": "Número de transacción", "trans_type": "Tipo de transacción", "credits": "C<PERSON>dit<PERSON>", "updated_at": "Actualizado el", "status": "Estado"}}, "api_keys": {"title": "Claves API", "tip": "Mantén tus claves API seguras para evitar filtraciones", "no_api_keys": "No hay claves API", "create_api_key": "Crear clave API", "table": {"name": "Nombre", "key": "Clave", "created_at": "Creado el"}, "form": {"name": "Nombre", "name_placeholder": "Nombre de clave API", "submit": "Enviar"}}, "blog": {"title": "Blog", "description": "Noticias, recursos y actualizaciones de ShipAny", "read_more_text": "<PERSON><PERSON>"}, "my_invites": {"title": "Mis invitaciones", "description": "Ver tus registros de invitaciones", "no_invites": "No se encontraron registros de invitaciones", "my_invite_link": "Mi enlace de invitación", "edit_invite_link": "Editar enlace de invitación", "copy_invite_link": "<PERSON><PERSON>r enlace de invitación", "invite_code": "Código de invitación", "invite_tip": "Invita a 1 amigo a comprar ShipAny, gana $50 de recompensa.", "invite_balance": "Balance de recompensas por invitación", "total_invite_count": "Número total de invitaciones", "total_paid_count": "Número total de pagadores", "total_award_amount": "Cantidad total de recompensas", "update_invite_code": "Establecer c<PERSON> de invitación", "update_invite_code_tip": "Ingresa tu código de invitación personalizado", "update_invite_button": "Guardar", "no_orders": "No puedes invitar a otros antes de comprar ShipAny", "no_affiliates": "No tienes permiso para invitar a otros, por favor contáctanos para obtener permiso.", "table": {"invite_time": "Tiempo de invitación", "invite_user": "<PERSON><PERSON><PERSON> invitado", "status": "Estado", "reward_percent": "Porcentaje de recompensa", "reward_amount": "Cantidad de recompensa", "pending": "Pendiente", "completed": "Completado"}}, "feedback": {"title": "Comentarios", "description": "Nos gustaría escuchar qué funcionó bien o cómo podemos mejorar la experiencia del producto.", "submit": "Enviar", "loading": "Enviando...", "contact_tip": "Otras formas de contactarnos", "rating_tip": "¿Qué te parece ShipAny?", "placeholder": "Deja tus comentarios aquí..."}, "my_creations": {"title": "Mis creaciones", "description": "Ver y gestionar todos tus bocetos generados por IA", "metadata": {"title": "Mis Obras de Arte IA - Ver y Gestionar Imágenes Generadas | ImageToSketch.ai", "description": "Visualiza, gestiona y descarga todos tus bocetos y obras de arte generadas por IA. Accede a tu galería completa de imágenes transformadas con varios estilos artísticos.", "keywords": "mi arte IA, bocetos generados, galería arte IA, gestionar creaciones, descargar imágenes IA, colección bocetos, historial arte IA"}, "filters": {"all_styles": "Todos los estilos", "all_status": "Todos los estados", "styles": {"pencil": "Lá<PERSON>z", "charcoal": "<PERSON><PERSON><PERSON>", "sketcher": "Boceto", "comicnoir": "Cómic noir", "crosshatch": "<PERSON><PERSON><PERSON>", "colorsketch": "Boceto a color", "crayon": "Crayón", "penart": "Arte con pluma", "inkwash": "La<PERSON>do de tinta", "graphicnovel": "Novela gráfica"}, "status": {"completed": "Completado", "generating": "Generando", "error": "Error"}}, "actions": {"load_more": "<PERSON>gar más", "delete_image": "Eliminar imagen", "click_to_preview": "Haz clic para vista previa", "download": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "preview": {"title": "Boceto estilo {style}", "close_hint": "<PERSON>z clic fuera para cerrar"}, "delete_confirm": {"title": "Eliminar imagen", "message": "¿Estás seguro de que quieres eliminar esta imagen? Esta acción no se puede deshacer.", "image_alt": "Imagen a eliminar"}, "empty": {"message": "No se encontraron creaciones"}, "loading": "Cargando..."}, "imagetoimage": {"name": "imagen a imagen", "title": "Generador IA de transformación de imágenes", "description": "Aprovecha la IA para transformar, estilizar y recrear cualquier imagen sin esfuerzo", "upload": {"title": "Subida de imagen", "description": "Sube una imagen para usar como referencia, máximo 5 imágenes permitidas.", "button": "Subir imagen", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "Imágenes subidas"}, "prompt": {"title": "Prompt", "description": "Describe lo que quieres cambiar en la imagen", "placeholder": "Ejemplo: Cambiar el fondo a cielo azul.", "maxLength": "1000"}, "aspectRatio": {"title": "Relación de aspecto", "description": "Elige la relación de aspecto que quieres usar", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "outputs": {"title": "Número de salidas", "description": "Elige el número de salidas que quieres generar"}, "generate": {"button": "<PERSON><PERSON> im<PERSON>", "generating": "Generando...", "icon": "✨"}, "preview": {"empty": {"title": "Sube una imagen para comenzar", "description": "Sube una imagen y describe los cambios que quieres ver para generar resultados increíbles"}, "navigation": {"of": "de"}, "download": "<PERSON><PERSON><PERSON> imagen", "transformLabel": "Transformado a estilo <PERSON>"}, "messages": {"maxImagesError": "Máximo {max} imágenes permitidas", "fileTooLarge": "El archivo {name} es demasiado grande. El tamaño máximo es {size}MB", "formatNotSupported": "El formato del archivo {name} no es compatible", "uploadSuccess": "{count} imagen(es) subida(s) exitosamente", "noImages": "Por favor sube al menos una imagen", "noPrompt": "Por favor ingresa un prompt", "generateSuccess": "¡Imágenes generadas exitosamente!", "generateError": "Error al generar imágenes. Por favor intenta de nuevo."}}, "sketchtoimage": {"name": "boceto a imagen", "title": "Convertidor IA de Boceto a Imagen Realista", "description": "Transforma tus bocetos y dibujos en imágenes fotorrealistas con tecnología IA avanzada", "disabled": false, "header": {"title": "IA Boceto a Imagen", "description": "Transforma tus bocetos en imágenes realistas con IA"}, "upload": {"title": "Subida de boceto", "description": "Sube tu boceto o dibujo para transformarlo en una imagen realista, máximo 3 bocetos permitidos.", "button": "Subir boceto", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "Bocetos subidos", "file_types": "JPG, PNG, WebP", "max_size": "Máx {maxSize}MB", "uploaded_count": "Subido ({count}/{max})", "add_more": "Agregar más", "max_images_error": "Máximo {max} bocetos permitidos", "invalid_file_type": "{filename} no es un archivo de imagen válido", "file_size_error": "El tamaño del archivo excede {maxSize}MB", "success": "{filename} subido exitosamente", "failed": "Error al subir {filename}"}, "style": {"title": "Elegir estilo", "description": "Selecciona el estilo artístico para tus imágenes generadas", "more_styles": "<PERSON>ás estilos", "all_styles": "Todos los estilos", "style": "<PERSON><PERSON><PERSON>", "options": {"architecture": {"label": "Arquitectura", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/architecture.png", "description": "Estilo de dibujo arquitectónico y técnico"}, "default": {"label": "Por defecto", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/default.png", "description": "Conversión realista estándar"}, "photorealistic": {"label": "Fotorrealista", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/Photorealistic.png", "description": "Resultados ultra-realistas tipo foto"}, "digitalart": {"label": "Arte digital", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/digitalart.png", "description": "Estilo de arte digital moderno"}, "anime": {"label": "Anime", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/anime.png", "description": "Estilo de ilustración anime japonés"}, "interiordesign": {"label": "Diseño de interiores", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/interiordesign.png", "description": "Visualización de diseño de interiores"}, "3d": {"label": "3D", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/3d.png", "description": "Renderizado tridimensional"}, "pixar": {"label": "Pixar", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/pixar.png", "description": "Estilo de animación Pixar"}, "fantasy": {"label": "Fantasía", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/fantasy.png", "description": "Arte de fantasía y temas mágicos"}, "rpg": {"label": "RPG", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/rpg.png", "description": "Arte de personajes de juegos de rol"}, "comicbook": {"label": "<PERSON><PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/comicbook.png", "description": "Estilo de ilustración de cómic"}, "clay": {"label": "Arcilla", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/clay.png", "description": "Escultura y modelado en arcilla"}, "vectorart": {"label": "Arte vectorial", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/vectorart.png", "description": "Estilo de ilustración vectorial limpio"}, "minimalist": {"label": "Minimalista", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/minimalist.png", "description": "Diseño simple y limpio"}, "watercolor": {"label": "<PERSON><PERSON><PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/watercolor.png", "description": "Técnica de pintura en acuarela"}, "oilpainting": {"label": "Pintura al óleo", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/oilpainting.png", "description": "Estilo de pintura al óleo clásico"}, "gta": {"label": "GTA", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/gta.png", "description": "Estilo de juego Grand Theft Auto"}, "minecraft": {"label": "Minecraft", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/minecraft.png", "description": "Estilo de bloques de Minecraft"}}}, "creativity": {"title": "(Opcional) Ajustar creatividad", "description": "Controla qué tan creativa debe ser la IA con tu boceto", "similar": "Similar", "creative": "Creativo", "current": "Actual", "balanced": "Equilibrado", "slider_description": "Mueve el deslizador para ajustar el nivel de creatividad"}, "aspectRatio": {"title": "Relación de aspecto", "description": "Elige la relación de aspecto para tu imagen generada", "ratio": "Relación", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "generate": {"button": "Generar imagen", "generating": "Generando...", "icon": "🎨", "button_with_credits": "<PERSON>rar imagen ({credits} créditos)", "insufficient_credits": "Créditos insuficientes", "need_credits": "Necesitas {credits} créditos", "no_images_error": "Por favor sube al menos un boceto", "insufficient_credits_error": "Créditos insuficientes, por favor recarga", "success": "¡Imagen generada exitosamente!", "failed": "Error en la generación, por favor intenta de nuevo", "network_error": "Error en la generación, por favor verifica la conexión de red", "server_busy": "El servidor está ocupado, por favor intenta más tarde", "regenerate_success": "¡Regeneración exitosa!", "regenerate_failed": "Error en la regeneración", "regenerate_network_error": "Error en la regeneración, por favor intenta de nuevo"}, "delete_task": {"success": "<PERSON><PERSON> eliminada", "failed": "Error al eliminar"}, "download": {"success": "¡Imagen descargada exitosamente!", "trying_to_download": "Intentando descargar imagen...", "cannot_direct_download": "No se puede descargar directamente, por favor haz clic derecho para guardar", "link_copied": "Enlace de imagen copiado al portapapeles", "failed": "Error en la descarga"}, "time": {"just_now": "<PERSON><PERSON> mismo", "minutes_ago": "hace {minutes} minutos", "hours_ago": "hace {hours} horas", "days_ago": "hace {days} días"}, "preview": {"empty": {"title": "Sube un boceto para comenzar", "description": "Sube tu boceto y selecciona un estilo para generar imágenes realistas increíbles"}, "navigation": {"of": "de"}, "download": "<PERSON><PERSON><PERSON> imagen", "transformLabel": "Transformado a imagen realista", "generated_sketch": "Generado ", "modal": {"title": "Vista previa de imagen", "close": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "close_hint": "Haz clic fuera o presiona ESC para cerrar", "downloadFilename": "imagen_generada_{timestamp}.png"}}, "actions": {"add_more": "Agregar más", "add": "Agregar", "preview": "Vista previa", "download": "<PERSON><PERSON><PERSON>", "retry": "Reintentar", "regenerate": "<PERSON><PERSON><PERSON>", "open_image": "<PERSON><PERSON><PERSON> imagen", "close": "<PERSON><PERSON><PERSON>", "test_url": "Probar URL"}, "status": {"generating": "Generando", "completed": "Completado", "error": "Error"}, "alt_text": {"generated_image": "Imagen realista generada", "uploaded_sketch": "Boceto subido", "generated_sketch": "Boceto generado"}, "tooltips": {"preview_image": "Vista previa de imagen", "download_image": "<PERSON><PERSON><PERSON> imagen", "delete_image": "Eliminar imagen"}, "errors": {"failed_to_load": "Error al cargar"}, "messages": {"maxImagesError": "Máximo {max} bocetos permitidos", "fileTooLarge": "El archivo {name} excede el límite de tamaño de {size}MB", "formatNotSupported": "El formato del archivo {name} no es compatible", "uploadSuccess": "{count} bocetos subidos exitosamente", "noImages": "Por favor sube al menos un boceto", "generateSuccess": "Imagen generada exitosamente", "generateError": "Error en la generación de imagen"}, "examples": {"panel_title": "Ejemplos de resultados", "recent_tasks_title": "Tareas recientes", "view_all": "Ver todo", "no_tasks": "Aún no hay tareas", "no_tasks_description": "Genera tu primer boceto para verlo aquí", "loading_history": "Cargando historial...", "cta": {"title": "¿Quieres convertir tus propios bocetos?", "button": "Inicia sesión para comenzar"}, "items": [{"url": "/imgs/sketchtoimage/examples/example1.png", "title": "Generación de retratos", "description": "Transformar bocetos de retratos en fotos realistas"}, {"url": "/imgs/sketchtoimage/examples/example2.png", "title": "Creación de paisajes", "description": "Convertir dibujos de paisajes en fotos impresionantes"}, {"url": "/imgs/sketchtoimage/examples/example3.png", "title": "Visualización arquitectónica", "description": "Transformar bocetos arquitectónicos en renders realistas"}]}}}