"use client";

import { useAppContext } from "@/contexts/app";
import { useTranslations } from "next-intl";
import Icon from "@/components/icon";
import Image from "next/image";

export default function CreditsDisplay() {
  const { user } = useAppContext();
  const t = useTranslations();

  if (!user || !user.credits) {
    return null;
  }

  return (
<div className="flex items-center gap-1 px-2 py-1 rounded-md bg-secondary/50 text-sm font-medium">
   <Image
     src="/svg/coins.svg"
     alt="Credits"
     width={18}
     height={18}
     className="text-yellow-500"
   />
  <span className="text-foreground">{user.credits.left_credits}</span>
</div>
  );
} 