"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestEmailVerifyPage() {
  const [token, setToken] = useState("");
  const [result, setResult] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const testVerification = async () => {
    if (!token) {
      setResult("请输入token");
      return;
    }

    setIsLoading(true);
    setResult("");

    try {
      const response = await fetch(`/api/auth/email/verify?token=${token}`, {
        method: "GET",
        redirect: "manual"
      });
      
      if (response.type === "opaqueredirect" || response.status === 302 || response.status === 301) {
        const location = response.headers.get("location");
        setResult(`重定向到: ${location || "未知位置"}\n状态码: ${response.status}`);
      } else {
        const text = await response.text();
        setResult(`响应状态: ${response.status}\n响应内容: ${text}`);
      }
    } catch (error) {
      setResult(`错误: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const directTest = () => {
    if (!token) {
      setResult("请输入token");
      return;
    }
    
    window.location.href = `/api/auth/email/verify?token=${token}`;
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>测试邮箱验证</CardTitle>
          <CardDescription>
            输入验证token来测试邮箱验证流程
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="token" className="text-sm font-medium">
              验证Token
            </label>
            <Input
              id="token"
              type="text"
              placeholder="输入从邮件中获取的token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Button 
              onClick={testVerification} 
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? "测试中..." : "测试验证 (Fetch)"}
            </Button>
            
            <Button 
              onClick={directTest} 
              disabled={!token}
              className="w-full"
            >
              直接跳转测试
            </Button>
          </div>
          
          {result && (
            <div className="mt-4 p-3 bg-muted rounded-md">
              <pre className="text-sm whitespace-pre-wrap">{result}</pre>
            </div>
          )}
          
          <div className="text-sm text-muted-foreground">
            <p>提示：</p>
            <ul className="list-disc list-inside space-y-1">
              <li>检查开发服务器控制台的日志输出</li>
              <li>确认环境变量已正确设置</li>
              <li>Token有效期为15分钟</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 