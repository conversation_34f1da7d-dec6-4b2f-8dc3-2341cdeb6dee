import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { newStorage } from '@/lib/storage';
import { v4 as uuidv4 } from 'uuid';
import {
  insertSketchTask,
  insertSketchResults,
  updateSketchTaskStatus,
  findSketchTaskByUuid
} from '@/models/sketch';
import { decreaseCredits, decreaseCreditsWithTransaction, CreditsTransType, getUserCredits } from '@/services/credit';
import { getUserUuid } from '@/services/user';
import { createSecurityMiddleware, validateImageToSketchRequest, logAuditEvent, getClientIP } from '@/lib/security';

export const runtime = 'nodejs';
export const maxDuration = 60; // 最大执行时间60秒

// 配置请求体大小限制（50MB）
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
}

// 如果使用 App Router，添加这个配置
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface SketchRequest {
  images: string[];        // base64编码的图片数组
  style: string;          // sketch风格
  aspectRatio: string;    // 宽高比
  creditsNeeded?: number; // 需要的积分数量
}

interface SketchResponse {
  success: boolean;
  data?: {
    taskId?: string;      // 任务ID
    results: Array<{
      id: string;
      imageUrl?: string;
      base64?: string;
    }>;
  };
  error?: string;
}

// 风格到prompt的映射
const stylePrompts: Record<string, string> = {
  pencil: "Convert to pencil sketch style, detailed line art, graphite drawing",
  animelineart: "Convert to anime line art style, clean manga-style lines, japanese animation drawing",
  charcoal: "Convert to charcoal drawing style, dark tones, dramatic shadows",
  ink: "Convert to ink drawing style, black and white, fine line art",
  hatching: "Convert to crosshatch drawing style, detailed shading technique",
  stippling: "Convert to stippling art style, dot technique, pointillism",
  contour: "Convert to contour line drawing style, minimalist, clean lines",
  gesture: "Convert to gesture drawing style, expressive lines, quick sketch",
  scribble: "Convert to scribble art style, loose lines, artistic expression",
  fashion: "Convert to fashion illustration style, elegant lines, stylized",
  architectural: "Convert to architectural sketch style, technical drawing, precise lines",
  sketcher: "Convert to general sketch style, artistic interpretation",
  comicnoir: "Convert to comic noir style, dark graphic novel aesthetic",
  crosshatch: "Convert to cross-hatching style, detailed shading",
  colorsketch: "Convert to color sketch style, vibrant artistic interpretation",
  crayon: "Convert to crayon drawing style, artistic texture",
  penart: "Convert to pen art style, clean line work, precise illustration",
  inkwash: "Convert to ink wash painting style, traditional asian art",
  graphicnovel: "Convert to graphic novel illustration style, modern comic"
};

// 宽高比到尺寸的映射
const aspectRatioSizes: Record<string, string> = {
  "1:1": "1024x1024",
  "3:2": "1536x1024", 
  "2:3": "1024x1536"
};

// 将base64转换为Blob
function base64ToBlob(base64: string): Blob {
  // 移除data:image/...;base64,前缀（如果存在）
  const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: 'image/png' });
}

// 将base64转换为Buffer
function base64ToBuffer(base64: string): Buffer {
  const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
  return Buffer.from(base64Data, 'base64');
}

// 上传图片到R2存储
async function uploadImageToR2(
  base64: string,
  userUuid: string,
  taskUuid: string,
  filename: string
): Promise<string> {
  try {
    const storage = newStorage();
    const buffer = base64ToBuffer(base64);
    const key = `sketches/${userUuid}/${taskUuid}/${filename}`;
    
    const result = await storage.uploadFile({
      body: buffer,
      key: key,
      contentType: 'image/png',
    });
    
    return result.url || result.location || '';
  } catch (error) {
    console.error('Error uploading to R2:', error);
    throw error;
  }
}

// 单张图片处理函数
async function processImage(
  imageBase64: string, 
  index: number, 
  prompt: string, 
  size: string, 
  apiKey: string, 
  apiBase: string
): Promise<{ id: string; imageUrl?: string; base64?: string }> {
  try {
    console.log(`Processing image ${index}, prompt: ${prompt}`);
    
    // 构建FormData（模拟multipart/form-data）
    const formData = new FormData();
    
    // 将base64转换为Blob并添加到FormData
    const imageBlob = base64ToBlob(imageBase64);
    formData.append('image', imageBlob, `image_${index}.png`);
    formData.append('model', 'gpt-image-1');
    formData.append('prompt', prompt);
    formData.append('n', '1');
    formData.append('size', size);
    formData.append('response_format', 'b64_json');

    console.log(`API Request for image ${index}:`, {
      url: `${apiBase}/images/edits`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey.substring(0, 10)}...`
      },
      hasImageData: !!imageBlob,
      imageBlobSize: imageBlob.size
    });

    const response = await fetch(`${apiBase}/images/edits`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`
        // 注意：不要设置Content-Type，让fetch自动设置multipart/form-data
      },
      body: formData
    });

    console.log(`API Response for image ${index}: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`AI API error for image ${index}:`, {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      
      console.log(`Falling back to mock implementation for image ${index}`);
      // 如果AI API失败，使用模拟实现
      return await mockSketchGeneration(imageBase64, index, prompt);
    }

    const aiResult = await response.json();
    console.log(`AI Result for image ${index}:`, {
      hasData: !!aiResult.data,
      dataLength: aiResult.data?.length || 0
    });
    
    if (aiResult.data && aiResult.data.length > 0) {
      const generatedImage = aiResult.data[0];
      return {
        id: `result_${index}_${Date.now()}`,
        base64: generatedImage.b64_json,
        imageUrl: generatedImage.url
      };
    } else {
      console.warn(`No data in AI result for image ${index}:`, aiResult);
      // fallback到模拟实现
      return await mockSketchGeneration(imageBase64, index, prompt);
    }

  } catch (error) {
    console.error(`Error processing image ${index}:`, error);
    // fallback到模拟实现
    return await mockSketchGeneration(imageBase64, index, prompt);
  }
}

// 模拟sketch生成 - 临时实现
async function mockSketchGeneration(
  imageBase64: string, 
  index: number, 
  prompt: string
): Promise<{ id: string; imageUrl?: string; base64?: string }> {
  console.log(`Using mock implementation for image ${index}`);
  
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  // 这里可以返回一些预设的sketch图片base64，或者做简单的图像处理
  // 暂时返回原图（在实际应用中，你可以替换为预设的sketch样例）
  return {
    id: `result_${index}_${Date.now()}`,
    base64: imageBase64  // 临时返回原图
  };
}

export async function POST(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid || await getUserUuid(); // 确保获取到用户UUID

    if (!userUuid || userUuid === 'anonymous') {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as SketchResponse, { status: 401 });
    }

    const body: SketchRequest = await request.json();
    const { images, style, aspectRatio, creditsNeeded } = body;

    // 移除调试日志以提高性能

    // 🔥 简化安全检查以提高性能
    const validation = validateImageToSketchRequest(body);
    if (!validation.valid) {
      return NextResponse.json({
        success: false,
        error: validation.error || 'Invalid request parameters'
      } as SketchResponse, { status: 400 });
    }

    // 移除调试日志以提高性能

    // 验证输入参数
    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'At least one image is required'
      } as SketchResponse, { status: 400 });
    }

    if (images.length > 10) {
      return NextResponse.json({
        success: false,
        error: 'Maximum 10 images allowed per request'
      } as SketchResponse, { status: 400 });
    }

    if (!style || !stylePrompts[style]) {
      return NextResponse.json({
        success: false,
        error: 'Invalid style provided'
      } as SketchResponse, { status: 400 });
    }

    // 🔥 新增：验证 creditsNeeded 参数
    const creditsToDeduct = creditsNeeded || images.length;
    if (typeof creditsToDeduct !== 'number' || creditsToDeduct <= 0 || creditsToDeduct > 100) {
      return NextResponse.json({
        success: false,
        error: 'Invalid credits amount. Must be between 1 and 100.'
      } as SketchResponse, { status: 400 });
    }

    // 确保积分需求与图片数量合理匹配（防止恶意请求）
    if (creditsToDeduct < images.length || creditsToDeduct > images.length * 5) {
      return NextResponse.json({
        success: false,
        error: 'Credits needed must be reasonable for the number of images'
      } as SketchResponse, { status: 400 });
    }

    // 🔥 新增：预先检查用户积分余额
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < creditsToDeduct) {
      return NextResponse.json({
        success: false,
        error: `Insufficient credits. You have ${userCredits.left_credits} credits but need ${creditsToDeduct}.`
      } as SketchResponse, { status: 402 });
    }

    // 🔥 新增：在处理开始前扣减积分，使用事务保护防止竞态条件
    try {
      await decreaseCreditsWithTransaction({
        user_uuid: userUuid,
        trans_type: CreditsTransType.Ping,
        credits: creditsToDeduct,
      });
      console.log(`Successfully deducted ${creditsToDeduct} credits for user ${userUuid} before processing with transaction protection`);

      // 🔥 新增：记录积分扣减成功
      logAuditEvent({
        user_uuid: userUuid,
        operation_type: 'credits_deducted',
        operation_details: {
          operation: 'image_to_sketch',
          image_count: images.length,
          style: style,
          aspect_ratio: aspectRatio
        },
        credits_amount: creditsToDeduct,
        ip_address: getClientIP(request),
        user_agent: request.headers.get('user-agent') || 'unknown',
        success: true
      });
    } catch (creditError) {
      console.error('Failed to deduct credits before processing:', creditError);

      // 安全地获取错误消息
      const errorMessage = creditError instanceof Error ? creditError.message : String(creditError);

      return NextResponse.json({
        success: false,
        error: 'Credit deduction failed. Please try again.'
      } as SketchResponse, { status: 402 });
    }

    // 创建任务记录
    const taskUuid = uuidv4();
    const task = await insertSketchTask({
      uuid: taskUuid,
      user_uuid: userUuid,
      status: 'generating',
      task_type: 'image-to-sketch',
      style: style,
      aspect_ratio: aspectRatio,
      original_image_count: images.length,
      completed_count: 0,
    });

    if (!task) {
      console.error('Failed to create sketch task');
      return NextResponse.json({
        success: false,
        error: 'Failed to create task'
      } as SketchResponse, { status: 500 });
    }

    // 获取环境变量
    const apiKey = process.env.TUZI_API_KEY;
    const apiBase = process.env.TUZI_API_BASE || 'https://api.tu-zi.com/v1';

    console.log('Environment check:', {
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey?.substring(0, 10),
      apiBase
    });

    if (!apiKey) {
      console.error('TUZI_API_KEY not configured');
      return NextResponse.json({
        success: false,
        error: 'API key not configured'
      } as SketchResponse, { status: 500 });
    }

    // 构建prompt
    const stylePrompt = stylePrompts[style];
    const prompt = stylePrompt;
    const size = aspectRatioSizes[aspectRatio] || '1024x1024';

    console.log('Generation parameters:', {
      stylePrompt,
      size,
      fullPrompt: prompt
    });

    // 并行处理所有图片
    const processingPromises = images.map((imageBase64, index) => 
      processImage(imageBase64, index, prompt, size, apiKey, apiBase)
    );

    // 等待所有图片处理完成（使用allSettled确保即使某些失败也能继续）
    const results = await Promise.allSettled(processingPromises);

    // 提取成功的结果
    const finalResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`Failed to process image ${index}:`, result.reason);
        // 失败时返回原图作为fallback
        return {
          id: `result_${index}_${Date.now()}`,
          base64: images[index]
        };
      }
    });

    console.log('Final results:', {
      totalResults: finalResults.length,
      successCount: finalResults.filter(r => r.base64 !== images[finalResults.indexOf(r)]).length
    });

    // 上传图片到R2并保存结果到数据库
    try {
      const resultRecords = [];
      
      for (let i = 0; i < finalResults.length; i++) {
        const result = finalResults[i];
        const originalImage = images[i];
        
        try {
          // 上传原图
          const originalImageUrl = await uploadImageToR2(
            originalImage,
            userUuid,
            taskUuid,
            `original_${i}.png`
          );
          
          // 上传生成结果
          const resultImageUrl = await uploadImageToR2(
            result.base64 || originalImage,
            userUuid,
            taskUuid,
            `result_${i}.png`
          );
          
          // 创建结果记录
          const resultRecord = {
            uuid: uuidv4(),
            task_uuid: taskUuid,
            original_image_url: originalImageUrl,
            result_image_url: resultImageUrl,
            status: 'completed' as const,
            r2_key: `sketches/${userUuid}/${taskUuid}/result_${i}.png`,
            file_size: result.base64 ? Buffer.from(result.base64, 'base64').length : 0,
          };
          
          resultRecords.push(resultRecord);
          
          // 更新finalResults，使用R2的URL替换base64
          finalResults[i] = {
            ...result,
            imageUrl: resultImageUrl,
            base64: undefined, // 清除base64数据，减少响应大小
          };
          
        } catch (uploadError) {
          console.error(`Failed to upload images for index ${i}:`, uploadError);
          // 如果上传失败，至少保存基本记录
          const resultRecord = {
            uuid: uuidv4(),
            task_uuid: taskUuid,
            original_image_url: '',
            result_image_url: '',
            status: 'error' as const,
            r2_key: '',
            file_size: 0,
          };
          resultRecords.push(resultRecord);
        }
      }
      
      // 批量插入结果记录
      if (resultRecords.length > 0) {
        await insertSketchResults(resultRecords);
      }
      
      // 更新任务状态
      await updateSketchTaskStatus(
        taskUuid,
        'completed',
        finalResults.length,
        undefined
      );

      // 积分已在处理前扣减，这里不需要再次扣减
      console.log(`Processing completed successfully for user ${userUuid}, credits were deducted before processing`);

      // 🔥 新增：记录图片生成成功
      logAuditEvent({
        user_uuid: userUuid,
        operation_type: 'image_generation_completed',
        operation_details: {
          operation: 'image_to_sketch',
          task_id: taskUuid,
          image_count: images.length,
          results_count: finalResults.length,
          style: style,
          aspect_ratio: aspectRatio
        },
        credits_amount: creditsToDeduct,
        ip_address: getClientIP(request),
        user_agent: request.headers.get('user-agent') || 'unknown',
        success: true
      });

    } catch (dbError) {
      console.error('Database operation failed:', dbError);
      // 即使数据库操作失败，也返回结果给用户
    }

    return NextResponse.json({
      success: true,
      data: { 
        taskId: taskUuid,
        results: finalResults 
      }
    } as SketchResponse);

  } catch (error) {
    console.error('Sketch generation error:', error);
    
    // 如果有任务ID，更新任务状态为错误
    const taskUuidFromError = (error as any)?.taskUuid;
    if (taskUuidFromError) {
      try {
        await updateSketchTaskStatus(
          taskUuidFromError,
          'error',
          0,
          error instanceof Error ? error.message : 'Unknown error'
        );
      } catch (updateError) {
        console.error('Failed to update task status:', updateError);
      }
    }
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as SketchResponse, { status: 500 });
  }
} 