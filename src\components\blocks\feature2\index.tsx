"use client";

import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";
import Icon from "@/components/icon";

export default function Feature2({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-32">
      <div className="container">
        <div className="text-center mb-16">
          {section.label && (
            <Badge variant="outline" className="mb-4">
              {section.label}
            </Badge>
          )}
          <h2 className="text-4xl lg:text-5xl font-serif font-medium mb-6 bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-gray-100 dark:via-gray-300 dark:to-gray-100 bg-clip-text text-transparent leading-tight">
            {section.title}
          </h2>
          <p className="text-lg font-normal text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed">
            {section.description}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Vertical Timeline Line */}
            <div className="absolute left-5 top-0 bottom-0 w-px bg-gradient-to-b from-primary/20 via-primary/60 to-primary/20"></div>
            
            <div className="space-y-20">
              {section.items?.map((item, index) => (
                <div key={index} className="relative flex items-start gap-8 group">
                  {/* Step Number Circle with Effects */}
                  <div className="relative z-10 flex-shrink-0">
                    {/* Outer Glow Ring */}
                    <div className="absolute inset-0 w-10 h-10 rounded-full bg-gradient-to-r from-primary/30 to-primary/60 blur-sm opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    {/* Main Circle */}
                    <div className="relative w-10 h-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-lg shadow-primary/25 group-hover:shadow-xl group-hover:shadow-primary/40 transition-all duration-300 group-hover:scale-110">
                      {/* Inner Shine Effect */}
                      <div className="absolute inset-1 rounded-full bg-gradient-to-br from-white/30 to-transparent opacity-60"></div>
                      
                      {/* Number */}
                      <span className="relative text-base font-bold text-primary-foreground drop-shadow-sm">
                        {(item as any).step || (index + 1)}
                      </span>
                      
                      {/* Pulse Effect */}
                      <div className="absolute inset-0 rounded-full border-2 border-primary/50 animate-ping opacity-20"></div>
                    </div>
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 pt-1">
                    <h3 className="text-2xl font-serif font-medium mb-4 leading-tight group-hover:text-primary transition-colors duration-300">
                      Step {(item as any).step || (index + 1)}: {item.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed text-base">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Bottom CTA */}
        {/* <div className="text-center mt-16">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-muted border border-border rounded-full">
            <Icon name="RiSparklingLine" className="w-5 h-5" />
            <span className="font-medium">Ready to transform your images? Get started now!</span>
          </div>
        </div> */}
      </div>
    </section>
  );
}
