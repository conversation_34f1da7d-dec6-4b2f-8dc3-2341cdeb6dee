{"template": "my-app", "theme": "dark", "header": {"brand": {"title": "ImageToImage.ai", "logo": {"src": "/logo.png", "alt": "图像转图像AI"}, "url": "/"}, "nav": {"items": [{"title": "图像转素描", "url": "/"}, {"title": "图像转图像", "url": "/imagetoimage"}, {"title": "定价", "url": "/pricing"}]}, "show_sign": true, "show_theme": false, "show_locale": true}, "hero": {"title": "几小时内构建任何AI创业项目，而不是几天", "highlight_text": "ShipAny", "description": "ShipAny 是一个用于构建AI SaaS创业项目的NextJS模板。<br/>使用各种模板和组件快速启动。", "announcement": {"label": "2025", "title": "🎉 新年快乐", "url": "/#pricing"}, "tip": "🎁 2025年前享5折优惠", "buttons": [{"title": "开始使用", "icon": "RiFlashlightFill", "url": "/#pricing", "target": "_self", "variant": "default"}, {"title": "加入Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank", "variant": "outline"}], "show_happy_users": false, "show_badge": false}, "branding": {"title": "ShipAny 建立在巨人的肩膀上", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "React", "image": {"src": "/imgs/logos/react.svg", "alt": "React"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}, {"title": "Vercel", "image": {"src": "/imgs/logos/vercel.svg", "alt": "Vercel"}}]}, "imagetoimage": {"name": "imagetoimage", "title": "AI图像生成器", "description": "利用AI轻松地对任何图像进行变形、风格化和重新创作", "disabled": false, "upload": {"title": "图片上传", "description": "上传一张图片作为参考，最多允许5张图片。", "button": "上传图片", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 MB", "uploadedLabel": "已上传的图片"}, "prompt": {"title": "提示词", "description": "描述您想要在图片中更改的内容", "placeholder": "例如：将背景改为蓝天。", "maxLength": "1000"}, "aspectRatio": {"title": "长宽比", "description": "选择您想要使用的长宽比", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "outputs": {"title": "输出数量", "description": "选择您想要生成的输出数量"}, "generate": {"button": "生成图片", "generating": "生成中...", "icon": "✨"}, "preview": {"empty": {"title": "上传一张图片开始", "description": "上传一张图片并描述您想看到的变化，生成令人惊叹的结果"}, "navigation": {"of": " / "}, "download": "下载图片", "transformLabel": "转换为吉卜力风格"}, "messages": {"maxImagesError": "最多允许 {max} 张图片", "fileTooLarge": "文件 {name} 过大。最大尺寸为 {size}MB", "formatNotSupported": "文件 {name} 格式不支持", "uploadSuccess": "{count} 张图片上传成功", "noImages": "请至少上传一张图片", "noPrompt": "请输入提示词", "generateSuccess": "图片生成成功！", "generateError": "生成图片失败。请重试。"}}, "introduce": {"name": "introduce", "title": "什么是ShipAny", "label": "介绍", "description": "ShipAny 是一个用于构建AI SaaS创业项目的NextJS模板。内置了各种模板和组件。", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "即用型模板", "description": "从数十个生产就绪的AI SaaS模板中选择，快速启动您的项目。", "icon": "RiNextjsFill"}, {"title": "基础设施设置", "description": "立即访问可扩展的基础设施，内置最佳实践。", "icon": "RiDatabase2Line"}, {"title": "快速部署", "description": "在几小时内将您的AI SaaS应用程序部署到生产环境，而不是几天。", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "为什么选择ShipAny", "label": "优势", "description": "获得启动AI创业项目所需的一切——从即用型模板到技术支持。", "items": [{"title": "完整框架", "description": "基于Next.js构建，集成认证、支付和AI集成——一切开箱即用。", "icon": "RiNextjsFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "丰富的模板库", "description": "从各种AI SaaS模板中选择来启动您的项目——聊天机器人、图像生成等等。", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "技术指导", "description": "获得专门的支持并加入我们的开发者社区，确保您成功发布。", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "如何使用ShipAny启动", "description": "通过三个简单步骤让您的AI SaaS创业项目运行起来：", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "获取ShipAny", "description": "一次性付款购买ShipAny。查看您的邮箱获取代码和文档。", "image": {"src": "/imgs/features/5.png"}}, {"title": "启动您的项目", "description": "阅读文档并克隆ShipAny的代码。开始构建您的AI SaaS创业项目。", "image": {"src": "/imgs/features/6.png"}}, {"title": "定制您的项目", "description": "使用您的数据和内容修改模板。特定的AI功能需求。", "image": {"src": "/imgs/features/7.png"}}, {"title": "部署到生产环境", "description": "通过几个步骤将您的项目部署到生产环境，立即开始为客户提供服务。", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "ShipAny的核心功能", "description": "您快速高效启动AI SaaS创业项目所需的一切。", "items": [{"title": "Next.js模板", "description": "生产就绪的Next.js模板，具有SEO友好结构和i18n支持。", "icon": "RiNextjsFill"}, {"title": "认证与支付", "description": "集成Google OAuth、一键登录和Stripe支付处理。", "icon": "RiKey2Fill"}, {"title": "数据基础设施", "description": "内置Supabase集成，提供可靠且可扩展的数据存储。", "icon": "RiDatabase2Line"}, {"title": "一键部署", "description": "无缝部署到Vercel或Cloudflare，自动设置。", "icon": "RiCloudy2Fill"}, {"title": "业务分析", "description": "集成Google Analytics和Search Console用于跟踪增长。", "icon": "RiBarChart2Line"}, {"title": "AI就绪基础设施", "description": "预配置AI集成，内置积分系统和API销售。", "icon": "RiRobot2Line"}]}, "stats": {"name": "stats", "label": "数据", "title": "人们喜爱ShipAny", "description": "因为它易于使用且快速发布。", "icon": "FaRegHeart", "items": [{"title": "信赖用户", "label": "99+", "description": "客户"}, {"title": "构建组件", "label": "20+", "description": "组件"}, {"title": "快速发布", "label": "5", "description": "分钟"}]}, "testimonial": {"name": "testimonial", "label": "用户评价", "title": "用户对ShipAny的评价", "description": "听听使用ShipAny启动AI创业项目的开发者和创始人的声音。", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "AIWallpaper.shop创始人", "description": "ShipAny为我们节省了数月的开发时间。我们在短短2天内就推出了AI壁纸业务，一周内就获得了第一个付费客户！", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "HeyBeauty.ai CTO", "description": "预构建的AI基础设施改变了游戏规则。我们不必担心架构——只需专注于我们的AI美容技术并快速上线。", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "独立开发者", "description": "作为独立开发者，ShipAny给了我所需的一切——认证、支付、AI集成和美丽的UI。在一个周末就推出了我的SaaS！", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "Melodisco CEO", "description": "模板生产就绪且高度可定制。我们在几小时内而不是几个月内构建了我们的AI音乐平台。令人难以置信的上市时间！", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "GPTs.works技术负责人", "description": "ShipAny的基础设施非常稳固。我们从0扩展到1万用户，无需触及后端。这是我们AI创业的最佳投资。", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "创业公司创始人", "description": "从想法到上线只用了3天！ShipAny的模板和部署工具让我们能够极快地测试AI业务概念。", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "常见问题", "title": "关于ShipAny的常见问题", "description": "有其他问题？在Discord上或通过邮件联系我们。", "items": [{"title": "ShipAny到底是什么，它是如何工作的？", "description": "ShipAny是专门为构建AI SaaS创业项目设计的综合NextJS模板。它提供即用型模板、基础设施设置和部署工具，帮助您在几小时内而不是几天内启动AI业务。"}, {"title": "我需要高级技术技能才能使用ShipAny吗？", "description": "虽然基本的编程知识有帮助，但ShipAny设计得对开发者友好。我们的模板和文档让您轻松入门，即使您不是AI或云基础设施的专家。"}, {"title": "我可以用ShipAny构建什么类型的AI SaaS？", "description": "ShipAny支持广泛的AI应用，从内容生成到数据分析工具。我们的模板涵盖流行用例，如AI聊天机器人、内容生成器、图像处理应用等等。"}, {"title": "使用ShipAny启动通常需要多长时间？", "description": "使用ShipAny，您可以在几小时内拥有工作原型，在几小时内拥有生产就绪的应用程序。我们的一键部署和预配置基础设施显著减少了传统的数月开发周期。"}, {"title": "ShipAny基础设施包含什么？", "description": "ShipAny提供完整的基础设施堆栈，包括认证、数据库设置、API集成、支付处理和可扩展的云部署。一切都按照行业最佳实践预配置。"}, {"title": "我可以自定义模板以匹配我的品牌吗？", "description": "绝对可以！所有ShipAny模板都是完全可定制的。您可以修改设计、功能和特性以匹配您的品牌身份和特定业务需求，同时保持强大的底层基础设施。"}]}, "cta": {"name": "cta", "title": "发布您的第一个AI SaaS创业项目", "description": "从这里开始，使用ShipAny发布。", "buttons": [{"title": "获取ShipAny", "url": "https://shipany.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "阅读文档", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "ShipAny", "description": "ShipAny 是一个用于构建AI SaaS创业项目的NextJS模板。使用各种模板和组件快速启动。", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "copyright": "© 2025 • ShipAny 版权所有。", "nav": {"items": [{"title": "关于", "children": [{"title": "功能", "url": "/#feature", "target": "_self"}, {"title": "展示", "url": "/#showcase", "target": "_self"}, {"title": "定价", "url": "/#pricing", "target": "_self"}]}, {"title": "资源", "children": [{"title": "文档", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "组件", "url": "https://shipany.ai/components", "target": "_blank"}, {"title": "模板", "url": "https://shipany.ai/templates", "target": "_blank"}]}, {"title": "友情链接", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "邮箱", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}}