// 邮箱登录配置检查脚本
// 运行: node debug/check-email-config.js

console.log("🔍 检查邮箱登录配置...\n");

// 检查必需的环境变量
const requiredEnvVars = {
  'NEXT_PUBLIC_AUTH_EMAIL_ENABLED': process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED,
  'RESEND_API_KEY': process.env.RESEND_API_KEY ? '✓ 已设置' : '❌ 未设置',
  'NEXT_PUBLIC_EMAIL_FROM': process.env.NEXT_PUBLIC_EMAIL_FROM,
  'NEXTAUTH_URL': process.env.NEXTAUTH_URL,
  'NEXTAUTH_SECRET': process.env.NEXTAUTH_SECRET ? '✓ 已设置' : '❌ 未设置',
};

console.log("📋 环境变量检查:");
Object.entries(requiredEnvVars).forEach(([key, value]) => {
  const status = value ? '✓' : '❌';
  console.log(`${status} ${key}: ${value || '未设置'}`);
});

// 检查功能启用状态
console.log("\n🎛️ 功能状态:");
console.log(`邮箱登录: ${process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === 'true' ? '✅ 启用' : '❌ 禁用'}`);
console.log(`Google登录: ${process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === 'true' ? '✅ 启用' : '❌ 禁用'}`);
console.log(`GitHub登录: ${process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === 'true' ? '✅ 启用' : '❌ 禁用'}`);

// 提供修复建议
console.log("\n💡 修复建议:");
if (process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED !== 'true') {
  console.log("- 在 .env.development 中设置 NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true");
}
if (!process.env.RESEND_API_KEY) {
  console.log("- 在 .env.development 中设置 RESEND_API_KEY");
}
if (!process.env.NEXT_PUBLIC_EMAIL_FROM) {
  console.log("- 在 .env.development 中设置 NEXT_PUBLIC_EMAIL_FROM");
}
if (!process.env.NEXTAUTH_SECRET) {
  console.log("- 在 .env.development 中设置 NEXTAUTH_SECRET");
}

console.log("\n📖 查看 QUICK_START.md 获取完整配置说明"); 