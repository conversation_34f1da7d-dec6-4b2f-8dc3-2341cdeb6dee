import CredentialsProvider from "next-auth/providers/credentials";
import GitHub<PERSON>rovider from "next-auth/providers/github";
import GoogleProvider from "next-auth/providers/google";
import EmailProvider from "next-auth/providers/email";
import { NextAuthConfig } from "next-auth";
import { Provider } from "next-auth/providers/index";
import type { JWT } from "next-auth/jwt";
import { User } from "@/types/user";
import { getClientIp } from "@/lib/ip";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import { saveUser } from "@/services/user";
import { handleSignInUser } from "./handler";
import { DrizzleAdapter } from "@auth/drizzle-adapter";
import { db } from "@/db";
import { accounts, sessions, users, verificationTokens } from "@/db/schema";

let providers: Provider[] = [];

// Google One Tap Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true" &&
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
) {
  providers.push(
    CredentialsProvider({
      id: "google-one-tap",
      name: "google-one-tap",

      credentials: {
        credential: { type: "text" },
      },

      async authorize(credentials, req) {
        const googleClientId = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID;
        if (!googleClientId) {
          console.log("invalid google auth config");
          return null;
        }

        const token = credentials!.credential;

        const response = await fetch(
          "https://oauth2.googleapis.com/tokeninfo?id_token=" + token
        );
        if (!response.ok) {
          console.log("Failed to verify token");
          return null;
        }

        const payload = await response.json();
        if (!payload) {
          console.log("invalid payload from token");
          return null;
        }

        const {
          email,
          sub,
          given_name,
          family_name,
          email_verified,
          picture: image,
        } = payload;
        if (!email) {
          console.log("invalid email in payload");
          return null;
        }

        const user = {
          id: sub,
          name: [given_name, family_name].join(" "),
          email,
          image,
          emailVerified: email_verified ? new Date() : null,
        };

        return user;
      },
    })
  );
}

// Google Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" &&
  process.env.AUTH_GOOGLE_ID &&
  process.env.AUTH_GOOGLE_SECRET
) {
  providers.push(
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    })
  );
}

// Github Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" &&
  process.env.AUTH_GITHUB_ID &&
  process.env.AUTH_GITHUB_SECRET
) {
  providers.push(
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    })
  );
}

// Email Auth (Magic Link) - 暂时禁用，等适配器问题解决后再启用
if (
  false && // 暂时禁用
  process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true" &&
  process.env.RESEND_API_KEY &&
  process.env.NEXT_PUBLIC_EMAIL_FROM
) {
  providers.push(
    EmailProvider({
      server: {
        host: "smtp.resend.com",
        port: 587,
        auth: {
          user: "resend",
          pass: process.env.RESEND_API_KEY,
        },
      },
      from: process.env.NEXT_PUBLIC_EMAIL_FROM,
    })
  );
}

// 添加邮箱登录的 Credentials Provider
if (process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true") {
  providers.push(
    CredentialsProvider({
      id: "email-login",
      name: "Email Login",
      credentials: {
        token: { type: "text" },
      },
      async authorize(credentials, req) {
        if (!credentials?.token) {
          return null;
        }

        // 这里我们需要验证token并返回用户信息
        // 但是我们不能在这里直接访问数据库，因为这是在edge运行时
        // 所以我们需要通过API来验证token
        try {
          const response = await fetch(`${process.env.NEXTAUTH_URL}/api/auth/email/verify-token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token: credentials.token }),
          });

          if (!response.ok) {
            return null;
          }

          const userData = await response.json();
          return {
            id: userData.uuid,
            email: userData.email,
            name: userData.nickname,
            image: userData.avatar_url,
          };
        } catch (error) {
          console.error('Email login authorization failed:', error);
          return null;
        }
      },
    })
  );
}

export const providerMap = providers
  .map((provider) => {
    if (typeof provider === "function") {
      const providerData = provider();
      return { id: providerData.id, name: providerData.name };
    } else {
      return { id: provider.id, name: provider.name };
    }
  })
  .filter((provider) => provider.id !== "google-one-tap");

export const authOptions: NextAuthConfig = {
  // 暂时不使用适配器，让 Google 和 GitHub 登录正常工作
  // 邮箱登录功能稍后单独配置适配器
  providers,
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      const isAllowedToSignIn = true;
      if (isAllowedToSignIn) {
        return true;
      } else {
        // Return false to display a default error message
        return false;
        // Or you can return a URL to redirect to:
        // return '/unauthorized'
      }
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    async session({ session, token, user }) {
      if (token && token.user && token.user) {
        session.user = token.user;
      }
      return session;
    },
    async jwt({ token, user, account }) {
      // 如果token已经包含用户信息（邮箱登录的情况），直接返回
      if (token.user && typeof token.user === 'object' && 'uuid' in token.user && token.user.uuid) {
        console.log("JWT callback: Using existing token.user for email login");
        return token;
      }

      // 处理OAuth登录的情况
      try {
        if (!user || !account) {
          console.log("JWT callback: No user or account, returning existing token");
          return token;
        }

        console.log("JWT callback: Processing OAuth login");
        const userInfo = await handleSignInUser(user, account);
        if (!userInfo) {
          throw new Error("save user failed");
        }

        token.user = {
          uuid: userInfo.uuid,
          email: userInfo.email,
          nickname: userInfo.nickname,
          avatar_url: userInfo.avatar_url,
          created_at: userInfo.created_at,
        };

        return token;
      } catch (e) {
        console.error("jwt callback error:", e);
        return token;
      }
    },
  },
};
