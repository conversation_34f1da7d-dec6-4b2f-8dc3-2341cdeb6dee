import { Header } from "@/types/blocks/header";
import { Hero } from "@/types/blocks/hero";
import { Section } from "@/types/blocks/section";
import { Footer } from "@/types/blocks/footer";
import { Pricing } from "@/types/blocks/pricing";

// ImageToSketch 特定的类型定义
export interface ImageToSketchSection {
  name: string;
  title: string;
  description: string;
  disabled?: boolean;
  upload: {
    title: string;
    description: string;
    button: string;
    formats?: string;
    maxSize?: string;
    uploadedLabel?: string;
  };
  style: {
    title: string;
    description: string;
    options: {
      [key: string]: {
        label: string;
        previewImage: string;
        description: string;
      };
    };
  };
  aspectRatio: {
    title: string;
    description: string;
    options: {
      "1:1": string;
      "3:2": string;
      "2:3": string;
    };
  };
  generate: {
    button: string;
    generating: string;
    icon?: string;
    button_with_credits?: string;
    insufficient_credits?: string;
    need_credits?: string;
  };
  preview?: {
    empty?: {
      title: string;
      description: string;
    };
    navigation?: {
      of: string;
    };
    download?: string;
    transformLabel?: string;
    modal?: {
      title?: string;
      close?: string;
      download?: string;
      closeHint?: string;
      downloadFilename?: string;
    };
  };
  actions?: {
    add_more?: string;
    add?: string;
    preview?: string;
    download?: string;
    retry?: string;
    regenerate?: string;
    open_image?: string;
    close?: string;
  };
  status?: {
    generating?: string;
    completed?: string;
    error?: string;
  };
  alt_text?: {
    generated_sketch?: string;
    uploaded_image?: string;
  };
  tooltips?: {
    preview_image?: string;
    download_image?: string;
    delete_image?: string;
  };
  errors?: {
    failed_to_load?: string;
    download_failed_title?: string;
    download_failed_message?: string;
    download_steps?: string[];
  };
  messages?: {
    maxImagesError?: string;
    fileTooLarge?: string;
    formatNotSupported?: string;
    uploadSuccess?: string;
    noImages?: string;
    generateSuccess?: string;
    generateError?: string;
  };
  examples?: any;
  labels?: {
    style?: string;
    aspectRatio?: string;
    ratio?: string;
    upload?: string;
    generate?: string;
  };
}

// 在现有文件中添加新类型
interface SketchToImageSection {
  name: string;
  title: string;
  description: string;
  disabled?: boolean;
  upload: {
    title: string;
    description: string;
    button: string;
    formats?: string;
    maxSize?: string;
    uploadedLabel?: string;
  };
  style: {
    title: string;
    description: string;
    options: {
      [key: string]: {
        label: string;
        previewImage: string;
        description: string;
      };
    };
  };
  aspectRatio: {
    title: string;
    description: string;
    options: {
      "1:1": string;
      "3:2": string;
      "2:3": string;
    };
  };
  generate: {
    button: string;
    generating: string;
    icon?: string;
    button_with_credits?: string;
    insufficient_credits?: string;
    need_credits?: string;
  };
  preview?: {
    empty?: {
      title: string;
      description: string;
    };
    navigation?: {
      of: string;
    };
    download?: string;
    transformLabel?: string;
    modal?: {
      title?: string;
      close?: string;
      download?: string;
      closeHint?: string;
      downloadFilename?: string;
    };
  };
  actions?: {
    add_more?: string;
    add?: string;
    preview?: string;
    download?: string;
    retry?: string;
    regenerate?: string;
    open_image?: string;
    close?: string;
  };
  status?: {
    generating?: string;
    completed?: string;
    error?: string;
  };
  alt_text?: {
    generated_sketch?: string;
    uploaded_image?: string;
  };
  tooltips?: {
    preview_image?: string;
    download_image?: string;
    delete_image?: string;
  };
  errors?: {
    failed_to_load?: string;
    download_failed_title?: string;
    download_failed_message?: string;
    download_steps?: string[];
  };
  messages?: {
    maxImagesError?: string;
    fileTooLarge?: string;
    formatNotSupported?: string;
    uploadSuccess?: string;
    noImages?: string;
    generateSuccess?: string;
    generateError?: string;
  };
  examples?: any;
  labels?: {
    style?: string;
    ratio?: string;
  };
}

// 原始主页类型
export interface LandingPage {
  header?: Header;
  imagetosketch?: ImageToSketchSection;
  branding?: Section;
  introduce?: Section;
  benefit?: Section;
  usage?: Section;
  feature?: Section;
  showcase?: Section;
  testimonial?: Section;
  faq?: Section;
  cta?: Section;
  footer?: Footer;
  pricing?: Pricing;
}

// 新增页面类型
export interface SketchToImageLandingPage {
  header?: Header;
  sketchtoimage?: SketchToImageSection;
  branding?: Section;
  introduce?: Section;
  benefit?: Section;
  usage?: Section;
  feature?: Section;
  showcase?: Section;
  stats?: Section;
  pricing?: Pricing;
  testimonial?: Section;
  faq?: Section;
  cta?: Section;
  footer?: Footer;
}

export interface ImageToImageLandingPage {
  header?: Header;
  hero?: Hero;
  branding?: Section;
  imagetoimage?: Section;
  introduce?: Section;
  benefit?: Section;
  usage?: Section;
  feature?: Section;
  showcase?: Section;
  stats?: Section;
  pricing?: Pricing;
  testimonial?: Section;
  faq?: Section;
  cta?: Section;
  footer?: Footer;
}

export interface PricingPage {
  title?: string;
  description?: string;
  keywords?: string;
  pricing?: Pricing;
  faq?: Section;
}

export interface ShowcasePage {
  showcase?: Section;
}
