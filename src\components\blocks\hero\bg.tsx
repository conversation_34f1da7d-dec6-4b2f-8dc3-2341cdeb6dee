export default function Bg() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1920 1080"
      fill="none"
      className="-z-50 absolute left-0 top-0 hidden opacity-30 [mask-image:linear-gradient(to_right,white,transparent,transparent,white)] lg:block"
    >
      <defs>
        {/* 黑色径向渐变定义 */}
        <radialGradient id="blackFog1" cx="50%" cy="50%" r="60%">
          <stop offset="0%" stopColor="rgba(0,0,0,0.4)" />
          <stop offset="40%" stopColor="rgba(0,0,0,0.2)" />
          <stop offset="70%" stopColor="rgba(0,0,0,0.1)" />
          <stop offset="100%" stopColor="rgba(0,0,0,0)" />
        </radialGradient>
        
        <radialGradient id="blackFog2" cx="50%" cy="50%" r="45%">
          <stop offset="0%" stopColor="rgba(0,0,0,0.3)" />
          <stop offset="50%" stopColor="rgba(0,0,0,0.15)" />
          <stop offset="100%" stopColor="rgba(0,0,0,0)" />
        </radialGradient>
        
        <radialGradient id="blackFog3" cx="50%" cy="50%" r="35%">
          <stop offset="0%" stopColor="rgba(0,0,0,0.25)" />
          <stop offset="60%" stopColor="rgba(0,0,0,0.1)" />
          <stop offset="100%" stopColor="rgba(0,0,0,0)" />
        </radialGradient>
      </defs>
      
      <g>
        {/* 主背景 */}
        <rect width="1920" height="1080" fill="transparent" />
        
        {/* 团雾效果 - 多个不同大小和位置的圆形 */}
        
        {/* 左上角大团雾 */}
        <circle 
          cx="300" 
          cy="200" 
          r="400" 
          fill="url(#blackFog1)" 
          opacity="0.6"
        />
        
        {/* 右上角中等团雾 */}
        <circle 
          cx="1500" 
          cy="150" 
          r="300" 
          fill="url(#blackFog2)" 
          opacity="0.5"
        />
        
        {/* 中间偏左小团雾 */}
        <circle 
          cx="600" 
          cy="500" 
          r="250" 
          fill="url(#blackFog3)" 
          opacity="0.4"
        />
        
        {/* 右下角团雾 */}
        <circle 
          cx="1400" 
          cy="800" 
          r="350" 
          fill="url(#blackFog1)" 
          opacity="0.45"
        />
        
        {/* 底部中央小团雾 */}
        <circle 
          cx="950" 
          cy="900" 
          r="200" 
          fill="url(#blackFog2)" 
          opacity="0.35"
        />
        
        {/* 左下角团雾 */}
        <circle 
          cx="200" 
          cy="850" 
          r="280" 
          fill="url(#blackFog3)" 
          opacity="0.4"
        />
        
        {/* 顶部中央淡雾 */}
        <ellipse 
          cx="960" 
          cy="100" 
          rx="400" 
          ry="150" 
          fill="url(#blackFog2)" 
          opacity="0.25"
        />
        
        {/* 中央大范围淡雾 */}
        <circle 
          cx="960" 
          cy="540" 
          r="500" 
          fill="url(#blackFog1)" 
          opacity="0.2"
        />
      </g>
    </svg>
  );
}
