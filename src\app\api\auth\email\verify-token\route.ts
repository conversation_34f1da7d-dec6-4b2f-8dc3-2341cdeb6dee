import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailVerificationTokens } from "@/db/schema";
import { eq } from "drizzle-orm";
import { saveUser } from "@/services/user";
import { getUuid } from "@/lib/hash";
import { User } from "@/types/user";
import { getClientIp } from "@/lib/ip";

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: "No token provided" }, { status: 400 });
    }

    // 查找验证token
    const verificationToken = await db()
      .select()
      .from(emailVerificationTokens)
      .where(eq(emailVerificationTokens.token, token))
      .limit(1);

    if (!verificationToken.length) {
      return NextResponse.json({ error: "Invalid token" }, { status: 400 });
    }

    const tokenRecord = verificationToken[0];

    // 检查是否过期
    const now = new Date();
    const isExpired = now > tokenRecord.expires_at;

    if (isExpired) {
      // 删除过期的token
      await db()
        .delete(emailVerificationTokens)
        .where(eq(emailVerificationTokens.id, tokenRecord.id));
      return NextResponse.json({ error: "Token expired" }, { status: 400 });
    }

    // 删除token（防止重复使用）
    await db()
      .delete(emailVerificationTokens)
      .where(eq(emailVerificationTokens.id, tokenRecord.id));

    // 创建用户信息
    const userInfo: User = {
      uuid: getUuid(),
      email: tokenRecord.email,
      nickname: tokenRecord.email.split("@")[0],
      avatar_url: "",
      signin_type: "email",
      signin_provider: "email",
      signin_openid: tokenRecord.email,
      created_at: new Date(),
      signin_ip: await getClientIp(),
    };

    // 使用现有的saveUser逻辑
    const savedUser = await saveUser(userInfo);

    if (!savedUser) {
      return NextResponse.json({ error: "User creation failed" }, { status: 500 });
    }

    return NextResponse.json({
      uuid: savedUser.uuid,
      email: savedUser.email,
      nickname: savedUser.nickname,
      avatar_url: savedUser.avatar_url,
    });

  } catch (error) {
    console.error("Email token verification error:", error);
    return NextResponse.json({ error: "Verification failed" }, { status: 500 });
  }
} 