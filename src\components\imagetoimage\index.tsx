// app/(main)/image-to-image/page.tsx
"use client";

import { useState, useRef } from "react";
import clsx from "clsx";
import {
  Upload,
  ChevronLeft,
  ChevronRight,
  X,
  Sparkles,
  Loader2,
  Download,
  Gem,
} from "lucide-react";
import { toast } from "sonner";

type Aspect = "1:1" | "3:2" | "2:3";
type OutputNum = 1 | 2 | 4 | 8;

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
}

interface ImageToImageConfig {
  name: string;
  title: string;
  description: string;
  upload: {
    title: string;
    description: string;
    button: string;
    formats: string;
    maxSize: string;
    uploadedLabel: string;
  };
  prompt: {
    title: string;
    description: string;
    placeholder: string;
    maxLength: string;
  };
  aspectRatio: {
    title: string;
    description: string;
    options: {
      "1:1": string;
      "3:2": string;
      "2:3": string;
    };
  };
  outputs: {
    title: string;
    description: string;
  };
  generate: {
    button: string;
    generating: string;
    icon: string;
  };
  preview: {
    empty: {
      title: string;
      description: string;
    };
    navigation: {
      of: string;
    };
    download: string;
    transformLabel: string;
  };
  messages: {
    maxImagesError: string;
    fileTooLarge: string;
    formatNotSupported: string;
    uploadSuccess: string;
    noImages: string;
    noPrompt: string;
    generateSuccess: string;
    generateError: string;
  };
}

interface ImageToImageProps {
  section: ImageToImageConfig;
}

export default function ImageToImage({ section }: ImageToImageProps) {
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [results, setResults] = useState<string[]>([]);
  const [index, setIndex] = useState(0);
  const [aspect, setAspect] = useState<Aspect>("1:1");
  const [outputs, setOutputs] = useState<OutputNum>(2);
  const fileRef = useRef<HTMLInputElement>(null);

  const demoImgs = ["/imgs/imagetoimage/imagetoimage1.png", "/imgs/imagetoimage/imagetoimage1.png", "/imgs/imagetoimage/imagetoimage1.png"];
  const [demoIdx, setDemoIdx] = useState(0);

  const maxImages = 5;
  const maxFileSize = 20; // MB

  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    if (uploadedImages.length + files.length > maxImages) {
      toast.error(section.messages.maxImagesError.replace("{max}", maxImages.toString()));
      return;
    }

    const newImages: UploadedImage[] = [];
    
    files.forEach((file) => {
      if (file.size > maxFileSize * 1024 * 1024) {
        toast.error(
          section.messages.fileTooLarge
            .replace("{name}", file.name)
            .replace("{size}", maxFileSize.toString())
        );
        return;
      }

      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
      const supportedFormats = [".jpg", ".jpeg", ".png", ".webp"];
      if (!supportedFormats.includes(fileExtension)) {
        toast.error(
          section.messages.formatNotSupported.replace("{name}", file.name)
        );
        return;
      }

      const imageUrl = URL.createObjectURL(file);
      newImages.push({
        id: crypto.randomUUID(),
        file,
        url: imageUrl,
        name: file.name,
      });
    });

    setUploadedImages((prev) => [...prev, ...newImages]);
    toast.success(
      section.messages.uploadSuccess.replace("{count}", newImages.length.toString())
    );
    e.target.value = "";
  };

  const generate = async () => {
    if (!uploadedImages.length) {
      toast.error(section.messages.noImages);
      return;
    }
    
    if (!prompt.trim()) {
      toast.error(section.messages.noPrompt);
      return;
    }

    setIsGenerating(true);
    
    try {
      await new Promise((r) => setTimeout(r, 1500)); // demo delay
      setResults(uploadedImages.map((u) => u.url));
      setIndex(0);
      toast.success(section.messages.generateSuccess);
    } catch (error) {
      toast.error(section.messages.generateError);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <>
      
      <main className="relative min-h-screen px-4 pb-24">
        {/* Header */}
        <header className="relative z-10 pt-16 text-center space-y-4">
          <h1 className="text-5xl md:text-6xl font-serif font-medium leading-[1.25]">
            {section.title}
          </h1>
          <p className="text-muted-foreground mt-4">
            {section.description}
          </p>
        </header>

        {/* Main Grid */}
        <section className="relative z-10 grid lg:grid-cols-2 items-start gap-10 max-w-6xl mx-auto mt-10 min-h-[500px]">
          {/* Left Panel */}
          <div className="space-y-8">
            {/* Upload Section */}
            <div className="space-y-3">
              <h2 className="font-serif text-xl">{section.upload.title}</h2>
              <p className="text-sm text-muted-foreground">
                {section.upload.description}
              </p>

              <label
                htmlFor="uploader"
                className={clsx(
                  "flex flex-col items-center justify-center py-8 border-2 border-dashed bg-muted/20 rounded-lg cursor-pointer transition",
                  uploadedImages.length >= maxImages
                    ? "border-border opacity-50 cursor-not-allowed"
                    : "hover:border-ring hover:bg-muted/30"
                )}
              >
                <Upload className="w-12 h-12 text-muted-foreground mb-4" />
                <span className="px-4 py-2 border border-border rounded-md text-sm">
                  {section.upload.button}
                </span>
                <span className="mt-3 text-xs text-muted-foreground">
                  {section.upload.formats} &nbsp; | &nbsp; {section.upload.maxSize}
                </span>
              </label>
              <input
                id="uploader"
                ref={fileRef}
                type="file"
                multiple
                accept="image/*"
                disabled={uploadedImages.length >= maxImages}
                onChange={handleUpload}
                className="hidden"
              />

              {uploadedImages.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">
                    {section.upload.uploadedLabel} ({uploadedImages.length}/{maxImages})
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    {uploadedImages.map((img) => (
                      <figure
                        key={img.id}
                        className="relative group rounded overflow-hidden"
                      >
                        <img
                          src={img.url}
                          alt={img.name}
                          className="h-24 w-full object-cover"
                        />
                        <button
                          onClick={() =>
                            setUploadedImages((prev) =>
                              prev.filter((i) => i.id !== img.id)
                            )
                          }
                          className="absolute top-1 right-1 p-1 bg-background/70 rounded-full opacity-0 group-hover:opacity-100 transition"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </figure>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Prompt */}
            <div className="space-y-3">
              <h2 className="font-serif text-xl">{section.prompt.title}</h2>
              <p className="text-sm text-muted-foreground">
                {section.prompt.description}
              </p>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder={section.prompt.placeholder}
                maxLength={1000}
                className="w-full min-h-[110px] bg-muted/20 border border-border rounded-md p-3 text-sm outline-none focus:border-ring placeholder:text-muted-foreground"
              />
              <p className="text-right text-xs text-muted-foreground">
                {prompt.length}/{section.prompt.maxLength}
              </p>
            </div>

            {/* Aspect Ratio */}
            <div className="space-y-3">
              <h2 className="font-serif text-xl">{section.aspectRatio.title}</h2>
              <p className="text-sm text-muted-foreground">
                {section.aspectRatio.description}
              </p>
              <div className="grid grid-cols-3 gap-3">
                {(["1:1", "3:2", "2:3"] as const).map((r) => (
                  <button
                    key={r}
                    onClick={() => setAspect(r)}
                    className={clsx(
                      "rounded-md border flex flex-col items-center justify-center gap-1 py-2 transition",
                      aspect === r
                        ? "bg-accent border-ring"
                        : "bg-muted/20 border-border hover:border-ring hover:bg-muted/30"
                    )}
                  >
                    <div
                      className={clsx(
                        "bg-muted-foreground rounded-sm",
                        r === "1:1" && "w-7 h-7",
                        r === "3:2" && "w-9 h-6",
                        r === "2:3" && "w-6 h-9"
                      )}
                    />
                    <span className="text-sm">{section.aspectRatio.options[r]}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Outputs */}
            <div className="space-y-3">
              <h2 className="font-serif text-xl">{section.outputs.title}</h2>
              <p className="text-sm text-muted-foreground">
                {section.outputs.description}
              </p>

              <div className="grid grid-cols-4 gap-3">
                {[1, 2, 4, 8].map((n) => (
                  <button
                    key={n}
                    onClick={() => setOutputs(n as OutputNum)}
                    className={clsx(
                      "relative py-3 rounded-md text-sm border transition",
                      outputs === n
                        ? "bg-accent border-ring"
                        : "bg-muted/20 border-border hover:border-ring hover:bg-muted/30"
                    )}
                  >
                    {n}
                    {(n === 4 || n === 8) && (
                      <Gem className="absolute -top-1.5 -right-1.5 w-3 h-3 text-muted-foreground" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={generate}
              disabled={isGenerating || !uploadedImages.length}
              className="w-full flex justify-center items-center gap-2 h-12 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  {section.generate.generating}
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4" />
                  {section.generate.button}
                </>
              )}
            </button>
          </div>

          {/* Right Preview Panel */}
          <div className="lg:sticky lg:top-40 flex flex-col mt-[65px]">
            {results.length === 0 && (
              <div className="h-[500px] border border-dashed border-border bg-muted/10 rounded-lg overflow-hidden relative">
                <img
                  src={demoImgs[demoIdx]}
                  className="w-full h-full object-cover"
                />
                <button
                  onClick={() =>
                    setDemoIdx((i) => (i === 0 ? demoImgs.length - 1 : i - 1))
                  }
                  className="absolute top-1/2 -translate-y-1/2 left-4 p-2 bg-background/50 rounded-full hover:bg-background/70 transition"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <button
                  onClick={() =>
                    setDemoIdx((i) => (i === demoImgs.length - 1 ? 0 : i + 1))
                  }
                  className="absolute top-1/2 -translate-y-1/2 right-4 p-2 bg-background/50 rounded-full hover:bg-background/70 transition"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
                <div className="absolute bottom-4 w-full flex justify-center gap-2">
                  {demoImgs.map((_, i) => (
                    <span
                      key={i}
                      className={clsx(
                        "size-2 rounded-full",
                        i === demoIdx ? "bg-primary" : "bg-muted-foreground"
                      )}
                    />
                  ))}
                </div>
              </div>
            )}

            {results.length > 0 && (
              <div className="space-y-4">
                {results.length > 1 && (
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-muted-foreground">
                      {index + 1} {section.preview.navigation.of} {results.length}
                    </span>
                    <div className="space-x-2">
                      <button
                        onClick={() => setIndex((i) => Math.max(i - 1, 0))}
                        disabled={index === 0}
                        className="p-2 border border-border rounded-md disabled:opacity-30 hover:bg-muted/20 transition"
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() =>
                          setIndex((i) =>
                            Math.min(i + 1, results.length - 1)
                          )
                        }
                        disabled={index === results.length - 1}
                        className="p-2 border border-border rounded-md disabled:opacity-30 hover:bg-muted/20 transition"
                      >
                        <ChevronRight className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}

                <div className="relative rounded-lg overflow-hidden shadow-lg">
                  <img
                    src={uploadedImages[index]?.url}
                    className="w-full object-cover aspect-square"
                  />
                  <img
                    src={results[index]}
                    className="absolute inset-0 w-full object-cover aspect-square animate-[fadeIn_.6s_ease]"
                    style={{ animationDelay: "0.12s" }}
                  />
                  <span className="absolute right-4 bottom-4 bg-background/60 backdrop-blur px-3 py-1 rounded-md text-sm font-medium">
                    {section.preview.transformLabel}
                  </span>
                </div>

                <button className="w-full py-2 border border-border rounded-md flex items-center justify-center gap-2 hover:bg-muted/20 transition">
                  <Download className="w-4 h-4" /> {section.preview.download}
                </button>

                {results.length > 1 && (
                  <div className="flex justify-center gap-2 mt-1">
                    {results.map((_, i) => (
                      <span
                        key={i}
                        className={clsx(
                          "size-2 rounded-full",
                          i === index ? "bg-primary" : "bg-muted-foreground"
                        )}
                      />
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </section>
      </main>
    </>
  );
}
