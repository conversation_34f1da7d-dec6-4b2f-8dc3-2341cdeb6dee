
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import ImageToSketch from "@/components/imagetosketch";
import Script from 'next/script';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }



  return {
    title: "ImageToSketch.ai - Transform Photos into Beautiful Sketches with AI",
    description: "Transform your photos into beautiful sketches with AI. Free online tool to convert images to pencil sketches, line art, and artistic drawings.",
    keywords: "image to sketch, photo to sketch, AI sketch, pencil drawing, line art, image converter",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://imagetosketch.ai';

  // 结构化数据
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "ImageToSketch.ai",
    "url": baseUrl,
    "description": "Transform your photos into beautiful sketches with AI",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ImageToSketch.ai",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "description": "AI-powered image to sketch conversion platform"
  };

  const webAppSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "ImageToSketch.ai",
    "url": baseUrl,
    "description": "Transform your photos into beautiful sketches with AI",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "Image to Sketch Conversion",
      "Multiple Sketch Styles",
      "High Quality Output",
      "Fast Processing",
      "Free to Use"
    ]
  };

  return (
    <>
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
        strategy="beforeInteractive"
      />
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
        strategy="beforeInteractive"
      />
      <Script
        id="webapp-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(webAppSchema)
        }}
        strategy="beforeInteractive"
      />
      {page.imagetosketch && <ImageToSketch section={page.imagetosketch} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.cta && <CTA section={page.cta} />}
      {page.faq && <FAQ section={page.faq} />}
    </>
  );
}
