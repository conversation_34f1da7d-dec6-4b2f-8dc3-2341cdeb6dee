/**
 * 简单的结构化数据验证脚本
 * 使用fetch检查页面的结构化数据
 */

import fetch from 'node-fetch';
import { JSDOM } from 'jsdom';

const baseUrl = 'http://localhost:3001';

const testPages = [
  { url: '/', name: '首页', expectedSchemas: ['WebSite', 'Organization', 'SoftwareApplication', 'Service'] },
  { url: '/pricing', name: '定价页', expectedSchemas: ['WebSite', 'Organization', 'Product', 'FAQPage'] },
  { url: '/sketch-to-image', name: '素描转图片页', expectedSchemas: ['WebSite', 'Organization', 'Service'] },
  { url: '/imagetoimage', name: '图片转图片页', expectedSchemas: ['WebSite', 'Organization', 'Service'] }
];

async function validatePage(testPage) {
  try {
    console.log(`\n📄 验证页面: ${testPage.name} (${testPage.url})`);
    
    const response = await fetch(`${baseUrl}${testPage.url}`);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const html = await response.text();
    const dom = new JSDOM(html);
    const document = dom.window.document;
    
    // 查找所有JSON-LD脚本
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    
    console.log(`  ✅ 找到 ${jsonLdScripts.length} 个结构化数据脚本`);
    
    const foundSchemas = [];
    let validCount = 0;
    
    jsonLdScripts.forEach((script, index) => {
      try {
        const jsonData = JSON.parse(script.textContent);
        const schemaType = jsonData['@type'];
        
        foundSchemas.push(schemaType);
        validCount++;
        
        console.log(`    📋 脚本 ${index + 1} (${script.id}): ${schemaType}`);
        
        // 基本验证
        if (jsonData['@context'] !== 'https://schema.org') {
          console.log(`      ⚠️  警告: @context 应该是 https://schema.org`);
        }
        
        // 特定类型验证
        validateSchemaType(jsonData, schemaType);
        
      } catch (error) {
        console.log(`    ❌ 脚本 ${index + 1}: JSON 解析错误 - ${error.message}`);
      }
    });
    
    // 检查是否包含期望的Schema类型
    console.log(`\n  📊 Schema类型检查:`);
    testPage.expectedSchemas.forEach(expectedType => {
      if (foundSchemas.includes(expectedType)) {
        console.log(`    ✅ ${expectedType}: 已找到`);
      } else {
        console.log(`    ❌ ${expectedType}: 未找到`);
      }
    });
    
    return {
      success: true,
      schemasFound: foundSchemas.length,
      validSchemas: validCount
    };
    
  } catch (error) {
    console.log(`  ❌ 验证失败: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

function validateSchemaType(schema, type) {
  const validations = {
    WebSite: {
      required: ['name', 'url'],
      optional: ['description', 'potentialAction']
    },
    Organization: {
      required: ['name', 'url'],
      optional: ['logo', 'description', 'sameAs', 'contactPoint']
    },
    SoftwareApplication: {
      required: ['name', 'description', 'applicationCategory'],
      optional: ['offers', 'featureList']
    },
    Service: {
      required: ['name', 'description', 'provider'],
      optional: ['serviceType', 'areaServed']
    },
    Product: {
      required: ['name', 'description'],
      optional: ['offers', 'brand']
    },
    FAQPage: {
      required: ['mainEntity'],
      optional: []
    }
  };
  
  const validation = validations[type];
  if (!validation) {
    console.log(`      ℹ️  未知Schema类型: ${type}`);
    return;
  }
  
  const missingRequired = validation.required.filter(field => !schema[field]);
  if (missingRequired.length > 0) {
    console.log(`      ❌ 缺少必需字段: ${missingRequired.join(', ')}`);
  } else {
    console.log(`      ✅ 所有必需字段都存在`);
  }
  
  const presentOptional = validation.optional.filter(field => schema[field]);
  if (presentOptional.length > 0) {
    console.log(`      📝 包含可选字段: ${presentOptional.join(', ')}`);
  }
}

async function runValidation() {
  console.log('🚀 开始验证结构化数据...');
  console.log(`📍 基础URL: ${baseUrl}`);
  
  let totalSuccess = 0;
  let totalSchemas = 0;
  
  for (const testPage of testPages) {
    const result = await validatePage(testPage);
    if (result.success) {
      totalSuccess++;
      totalSchemas += result.schemasFound;
    }
  }
  
  console.log(`\n📈 验证总结:`);
  console.log(`  ✅ 成功验证页面: ${totalSuccess}/${testPages.length}`);
  console.log(`  📋 总结构化数据脚本: ${totalSchemas}`);
  console.log(`\n💡 建议:`);
  console.log(`  1. 使用 Google Rich Results Test 进一步验证: https://search.google.com/test/rich-results`);
  console.log(`  2. 使用 Schema.org Validator 验证: https://validator.schema.org/`);
  console.log(`  3. 在 Google Search Console 中监控结构化数据状态`);
  
  console.log('\n✨ 验证完成！');
}

// 运行验证
runValidation().catch(console.error);
