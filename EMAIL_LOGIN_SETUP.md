# 邮箱登录功能 - 设置和使用指南

## 概述

本项目已实现自定义邮箱登录功能，使用 Magic Link 的方式进行认证。用户输入邮箱地址后，系统会发送包含登录链接的邮件，用户点击链接即可完成登录。

## 功能特性

- ✅ 完全自定义的邮箱登录系统
- ✅ 使用 Resend 服务发送邮件
- ✅ 15分钟过期时间的验证token
- ✅ 与现有用户系统兼容
- ✅ 自动创建新用户并分配积分
- ✅ JWT session 管理
- ✅ 安全的token验证机制

## 数据库结构

已添加 `email_verification_tokens` 表：
```sql
CREATE TABLE email_verification_tokens (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 环境变量配置

请确保在 `.env.development` 文件中配置以下环境变量：

```env
# Next.js 基础配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-32-character-secret-key-here

# 认证提供商开关
NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true

# Resend 邮件服务配置
RESEND_API_KEY=re_your_resend_api_key
NEXT_PUBLIC_EMAIL_FROM=<EMAIL>

# 数据库配置
DATABASE_URL=your_database_url
```

## Resend 配置步骤

1. 在 [Resend.com](https://resend.com) 注册账户
2. 验证你的发送域名
3. 生成 API Key
4. 将 API Key 添加到环境变量中

## API 端点

### 1. 发送验证邮件
```
POST /api/auth/email/send
```

请求体：
```json
{
  "email": "<EMAIL>"
}
```

响应：
```json
{
  "message": "Verification email sent successfully",
  "success": true
}
```

### 2. 验证邮件链接
```
GET /api/auth/email/verify?token=<verification_token>
```

成功后会：
- 验证token有效性
- 创建/获取用户记录
- 设置JWT session cookie
- 重定向到首页

## 邮件模板

系统会发送包含以下内容的HTML邮件：
- 登录链接按钮
- 15分钟过期提醒
- 安全提示

## 工作流程

1. **用户输入邮箱** → 前端表单提交
2. **生成验证token** → 服务器创建唯一token
3. **发送邮件** → 通过Resend发送验证邮件
4. **用户点击链接** → 访问验证端点
5. **验证token** → 检查有效性和过期时间
6. **删除token** → 防止重复使用
7. **创建用户** → 使用现有的`saveUser`逻辑
8. **设置session** → 创建JWT并设置cookie
9. **重定向** → 跳转到首页

## 安全特性

- Token 15分钟过期
- 一次性使用token（使用后立即删除）
- 邮箱地址验证
- 旧token自动清理
- 过期token自动删除
- 安全的JWT签名

## 错误处理

系统会处理以下错误情况：
- 无效的邮箱格式
- 过期的验证token
- 已使用的token
- 用户创建失败
- 邮件发送失败

## 前端集成

登录表单已更新，支持：
- 邮箱输入验证
- 发送状态显示
- 错误消息提示
- 成功反馈

## 测试建议

1. 配置正确的环境变量
2. 确保数据库表已创建
3. 验证Resend API Key有效
4. 测试完整的登录流程
5. 检查邮件发送是否正常

## 故障排除

### 邮件发送失败
- 检查RESEND_API_KEY是否正确
- 确认发送域名已验证
- 检查发送邮箱地址格式

### Token验证失败
- 确认数据库连接正常
- 检查token是否过期
- 验证数据库表结构

### Session创建失败
- 确认NEXTAUTH_SECRET已设置
- 检查JWT库依赖
- 验证cookie设置

## 技术实现

- **后端**: Next.js API Routes
- **数据库**: PostgreSQL + Drizzle ORM
- **邮件服务**: Resend
- **认证**: 自定义JWT实现
- **前端**: React + TypeScript

## 兼容性

- 与现有Google/GitHub登录兼容
- 使用相同的用户表结构
- 共享用户管理逻辑
- 一致的session处理 