# 结构化数据实施指南

## 概述

本项目已实施完整的结构化数据（JSON-LD）方案，帮助Google和其他搜索引擎更好地理解网站内容，提升SEO效果和搜索结果展示。

## 已实施的结构化数据类型

### 1. 基础Schema（所有页面）
- **WebSite**: 网站基本信息和搜索功能
- **Organization**: 公司/品牌信息和联系方式

### 2. 首页专用Schema
- **SoftwareApplication**: AI工具应用信息
- **Service**: 图片转换服务描述
- **FAQPage**: 常见问题页面

### 3. 产品页面Schema
- **Service**: 特定功能服务（素描转图片、图片转图片）
- **FAQPage**: 产品相关FAQ

### 4. 定价页面Schema
- **Product**: 订阅计划产品信息
- **Offer**: 价格和优惠信息
- **FAQPage**: 定价相关FAQ

## 文件结构

```
src/
├── components/seo/
│   └── structured-data.tsx          # 结构化数据组件
├── lib/
│   └── structured-data-config.ts    # 配置和数据生成函数
└── app/[locale]/(default)/
    ├── layout.tsx                   # 基础结构化数据
    ├── page.tsx                     # 首页结构化数据
    ├── pricing/page.tsx             # 定价页结构化数据
    ├── sketch-to-image/page.tsx     # 素描转图片页结构化数据
    └── imagetoimage/page.tsx        # 图片转图片页结构化数据
```

## 使用方法

### 1. 基础使用
基础的WebSite和Organization schema已自动添加到所有页面：

```tsx
import { BaseStructuredData } from "@/components/seo/structured-data";

// 在layout中使用
<BaseStructuredData />
```

### 2. 添加FAQ结构化数据
```tsx
import { FAQStructuredData } from "@/components/seo/structured-data";

const faqData = page.faq?.items?.map(item => ({
  question: item.title || '',
  answer: item.description || ''
})) || [];

<FAQStructuredData faqs={faqData} />
```

### 3. 添加产品/服务结构化数据
```tsx
import { ServiceStructuredData } from "@/components/seo/structured-data";
import { getImageToSketchServiceSchema } from "@/lib/structured-data-config";

<ServiceStructuredData data={getImageToSketchServiceSchema()} />
```

### 4. 添加定价结构化数据
```tsx
import { PricingStructuredData } from "@/components/seo/structured-data";

const pricingPlans = page.pricing?.items?.map(item => ({
  name: item.title || '',
  description: item.description || '',
  price: item.price?.replace(/[^0-9.]/g, '') || '0',
  priceCurrency: item.currency || 'USD',
  billingPeriod: item.interval || 'month',
  features: item.features || [],
  url: item.button?.url || ''
})) || [];

<PricingStructuredData plans={pricingPlans} />
```

## 配置自定义

### 1. 修改基础信息
编辑 `src/lib/structured-data-config.ts` 中的配置：

```typescript
export const getOrganizationSchema = (): OrganizationSchema => ({
  name: 'Your Company Name',
  url: getBaseUrl(),
  logo: `${getBaseUrl()}/your-logo.png`,
  description: 'Your company description',
  sameAs: [
    'https://twitter.com/yourcompany',
    'https://facebook.com/yourcompany'
  ],
  contactPoint: {
    "@type": "ContactPoint",
    contactType: "customer service",
    email: "<EMAIL>",
    url: `${getBaseUrl()}/contact`
  }
});
```

### 2. 添加新的结构化数据类型
在 `src/components/seo/structured-data.tsx` 中添加新组件：

```typescript
export interface BlogPostSchema {
  headline: string;
  author: string;
  datePublished: string;
  dateModified: string;
  image: string;
}

export function BlogPostStructuredData({ data }: { data: BlogPostSchema }) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    headline: data.headline,
    author: {
      "@type": "Person",
      name: data.author
    },
    datePublished: data.datePublished,
    dateModified: data.dateModified,
    image: data.image
  };

  return (
    <Script
      id="blog-post-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}
```

## 验证和测试

### 1. Google Rich Results Test
使用Google的Rich Results Test工具验证结构化数据：
https://search.google.com/test/rich-results

### 2. Schema.org Validator
使用Schema.org官方验证器：
https://validator.schema.org/

### 3. 本地测试
在浏览器开发者工具中查看页面源码，确认JSON-LD脚本正确生成。

## 最佳实践

1. **保持数据准确性**: 确保结构化数据与页面实际内容一致
2. **避免重复**: 不要在同一页面重复添加相同类型的结构化数据
3. **定期更新**: 当页面内容变化时，及时更新对应的结构化数据
4. **测试验证**: 每次修改后都要进行验证测试
5. **监控效果**: 使用Google Search Console监控结构化数据的效果

## 常见问题

### Q: 为什么我的结构化数据没有在搜索结果中显示？
A: Google需要时间来处理和显示结构化数据，通常需要几周时间。确保数据格式正确且内容质量高。

### Q: 如何添加面包屑导航结构化数据？
A: 使用BreadcrumbStructuredData组件：

```tsx
import { BreadcrumbStructuredData } from "@/components/seo/structured-data";

const breadcrumbs = [
  { name: "Home", url: "/" },
  { name: "Products", url: "/products" },
  { name: "Current Page", url: "/products/current" }
];

<BreadcrumbStructuredData items={breadcrumbs} />
```

### Q: 如何为多语言网站配置结构化数据？
A: 当前实现已支持多语言，结构化数据会根据当前locale自动调整内容。

## 扩展建议

1. **添加评论和评分**: 为产品页面添加AggregateRating schema
2. **事件数据**: 如果有活动或网络研讨会，添加Event schema
3. **视频内容**: 为教程视频添加VideoObject schema
4. **本地业务**: 如果有实体店面，添加LocalBusiness schema
