"use client";

import { useEffect, useState } from "react";

export default function ImageToSketchBg() {
  const [stars, setStars] = useState<Array<{ 
    id: number; 
    x: number; 
    y: number; 
    size: number; 
    delay: number;
    duration: number;
  }>>([]);

  useEffect(() => {
    // 生成随机星星
    const generateStars = () => {
      const newStars = [];
      for (let i = 0; i < 80; i++) {
        newStars.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 2 + 1, // 1-4px
          delay: Math.random() * 5, // 0-5秒延迟
          duration: 1 + Math.random() * 3, // 1-4秒持续时间
        });
      }
      setStars(newStars);
    };

    generateStars();
  }, []);

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* 纯黑背景 */}
      <div className="absolute inset-0 bg-black" />
      
      {/* 闪烁的星星 */}
      <div className="absolute inset-0">
        {stars.map((star) => (
          <div
            key={star.id}
            className="absolute animate-twinkle"
            style={{
              left: `${star.x}%`,
              top: `${star.y}%`,
              animationDelay: `${star.delay}s`,
              animationDuration: `${star.duration}s`,
            }}
          >
            <div
              className="bg-white rounded-full opacity-80"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                boxShadow: `0 0 ${star.size * 2}px rgba(255, 255, 255, 0.5)`,
              }}
            />
          </div>
        ))}
      </div>

      {/* CSS动画定义 */}
      <style jsx>{`
        @keyframes twinkle {
          0%, 100% {
            opacity: 0.3;
            transform: scale(0.8);
          }
          50% {
            opacity: 1;
            transform: scale(1.2);
          }
        }
        
        .animate-twinkle {
          animation: twinkle ease-in-out infinite;
        }
      `}</style>
    </div>
  );
} 