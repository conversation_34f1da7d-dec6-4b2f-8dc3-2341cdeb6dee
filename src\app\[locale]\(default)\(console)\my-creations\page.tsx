import { getTranslations } from "next-intl/server";
import { getUserUuid } from "@/services/user";
import { redirect } from "next/navigation";
import MyCreationsClient from "./client";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/my-creations`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/my-creations`;
  }

  return {
    title: t("my_creations.metadata.title"),
    description: t("my_creations.metadata.description"),
    keywords: t("my_creations.metadata.keywords"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function MyCreationsPage() {
  const t = await getTranslations();
  const userUuid = await getUserUuid();

  if (!userUuid) {
    const callbackUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/my-creations`;
    redirect(`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`);
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {t("my_creations.title")}
        </h1>
        <p className="text-gray-600 mt-1">
          {t("my_creations.description")}
        </p>
      </div>
      
      <MyCreationsClient />
    </div>
  );
} 