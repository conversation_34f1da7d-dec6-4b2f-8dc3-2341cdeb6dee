import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { emailVerificationTokens } from "@/db/schema";

export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Testing database connection...");
    
    // 测试数据库连接
    const result = await db().select().from(emailVerificationTokens).limit(1);
    
    console.log("✅ Database connection successful");
    console.log("📊 Sample query result:", result);
    
    return NextResponse.json({ 
      success: true, 
      message: "Database connection successful",
      sampleData: result 
    });
    
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
} 