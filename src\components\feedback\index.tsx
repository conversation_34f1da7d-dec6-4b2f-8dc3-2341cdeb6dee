"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MessageCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { useState } from "react";

const feedbackTypes = [
  { value: "bug", label: "Bug Report" },
  { value: "feature", label: "Feature Request" },
  { value: "improvement", label: "Improvement" },
  { value: "question", label: "Question" },
  { value: "other", label: "Other" },
];

export default function Feedback() {
  const { user, setShowSignModal, showFeedback, setShowFeedback } =
    useAppContext();

  const [feedback, setFeedback] = useState("");
  const [feedbackType, setFeedbackType] = useState("feature");
  const [loading, setLoading] = useState<boolean>(false);

  const maxLength = 1500;

  const handleSubmit = async () => {
    if (!user) {
      setShowSignModal(true);
      return;
    }

    if (!feedback.trim()) {
      toast.error("Please describe your feedback in detail");
      return;
    }

    if (!feedbackType) {
      toast.error("Please select a feedback type");
      return;
    }

    try {
      setLoading(true);

      const req = {
        content: feedback,
        type: feedbackType,
      };

      const resp = await fetch("/api/add-feedback", {
        method: "POST",
        body: JSON.stringify(req),
      });

      if (!resp.ok) {
        toast.error("Submit failed with status " + resp.status);
        return;
      }

      const { code, message } = await resp.json();
      if (code !== 0) {
        toast.error(message);
        return;
      }

      toast.success("Thank you for your feedback!");

      setFeedback("");
      setFeedbackType("feature");
      setShowFeedback(false);
    } catch (error) {
      toast.error("Failed to submit, please try again later");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFeedback("");
    setFeedbackType("feature");
    setShowFeedback(false);
  };

  return (
    <div className="fixed bottom-8 right-8 z-50">
      <Dialog open={showFeedback} onOpenChange={setShowFeedback}>
        <DialogTrigger asChild>
          <Button
            size="icon"
            className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={() => setShowFeedback(true)}
          >
            <MessageCircle className="h-6 w-6" />
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          {/* Header */}
          <DialogHeader>
            <DialogTitle className="text-2xl font-semibold">
              Help Us Improve
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Please log in to submit feedback so we can respond to you promptly
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 mt-6">
            {/* Feedback Type Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <span className="text-red-500">*</span>
                What type of feedback do you have?
              </label>
              <Select value={feedbackType} onValueChange={setFeedbackType}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select feedback type" />
                </SelectTrigger>
                <SelectContent>
                  {feedbackTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Feedback Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <span className="text-red-500">*</span>
                Please describe in detail
              </label>
              <div className="relative">
                <Textarea
                  placeholder="Describe a new feature or improvement you'd like to see, you can provide other potential solutions or mention similar features from other products you've used"
                  value={feedback}
                  onChange={(e) => {
                    if (e.target.value.length <= maxLength) {
                      setFeedback(e.target.value);
                    }
                  }}
                  className="min-h-[120px] resize-none pr-20"
                  maxLength={maxLength}
                />
                <div className="absolute bottom-3 right-3 text-sm text-muted-foreground">
                  {feedback.length}/{maxLength}
                </div>
              </div>
            </div>
          </div>

          {/* Footer with buttons */}
          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading || !feedback.trim()}
            >
              {loading ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
