#!/usr/bin/env node

/**
 * 简单的数据库连接测试
 */

import { config } from 'dotenv';
config({ path: '.env.production' });

console.log('🔍 测试数据库连接...\n');

const databaseUrl = process.env.DATABASE_URL;
console.log('📋 数据库连接信息:');
console.log('DATABASE_URL:', databaseUrl ? databaseUrl.replace(/:[^:@]*@/, ':****@') : '未设置');

if (!databaseUrl) {
  console.error('❌ 错误: DATABASE_URL 环境变量未设置');
  process.exit(1);
}

// 解析连接字符串
try {
  const connectionConfig = new URL(databaseUrl);
  console.log('\n🔗 连接参数:');
  console.log('主机:', connectionConfig.hostname);
  console.log('端口:', connectionConfig.port);
  console.log('数据库:', connectionConfig.pathname.slice(1));
  console.log('用户名:', connectionConfig.username);
  console.log('SSL模式:', connectionConfig.searchParams.get('sslmode') || '未设置');
} catch (error) {
  console.error('❌ 错误: 无法解析数据库连接字符串');
  console.error(error.message);
  process.exit(1);
}

console.log('\n💡 建议解决方案:');
console.log('1. 登录 Supabase 控制台检查项目状态');
console.log('2. 确认项目 ID: vssgaguvajidlturkshx');
console.log('3. 重置数据库密码并更新 .env.production');
console.log('4. 检查 Supabase 账户配额和计费状态');
console.log('5. 尝试使用 Pooler 连接（端口 6543）');

console.log('\n🔧 可能的修复方案:');
console.log('方案1 - 使用 Pooler 连接:');
console.log('DATABASE_URL = "postgresql://postgres.vssgaguvajidlturkshx:<EMAIL>:6543/postgres"');
console.log('\n方案2 - 直连:');
console.log('DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"');
console.log('\n方案3 - 添加连接参数:');
console.log('DATABASE_URL = "...?sslmode=require&connect_timeout=10&pool_timeout=10"');
