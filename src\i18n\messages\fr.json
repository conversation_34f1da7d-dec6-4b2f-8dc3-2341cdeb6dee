{"metadata": {"title": "Convertisseur IA Image vers Esquisse | Transformez vos photos en art dessiné | ImageToSketch.ai", "description": "Outil professionnel IA pour convertir vos photos en magnifiques esquisses dessinées à la main. Support pour crayon, fusain, encre et autres styles artistiques, haute résolution, licence commerciale. Essayez gratuitement maintenant !", "keywords": "convertir image en esquisse, photo vers dessin, générateur IA esquisse, image vers ligne art, outil esquisse en ligne, artistique photo, création dessins, art IA"}, "user": {"sign_in": "Se connecter", "sign_out": "Se déconnecter", "credits": "Crédits", "my_creations": "Mes créations", "api_keys": "Clés API", "my_orders": "<PERSON><PERSON> commandes", "user_center": "Centre utilisateur", "admin_system": "Système d'administration"}, "sign_modal": {"sign_in_title": "Veuillez vous connecter pour continuer", "sign_in_description": "Nous vous enverrons un lien magique par email", "sign_up_title": "<PERSON><PERSON><PERSON> un compte", "sign_up_description": "<PERSON><PERSON>er un nouveau compte", "email_title": "<PERSON><PERSON><PERSON> email", "email_placeholder": "Saisissez votre email", "password_title": "Mot de passe", "password_placeholder": "Saisissez votre mot de passe", "forgot_password": "Mot de passe oublié ?", "or": "Ou connectez-vous avec", "continue": "<PERSON><PERSON><PERSON>", "no_account": "Pas de compte ?", "email_sign_in": "Connexion par email", "google_sign_in": "Google", "github_sign_in": "Connexion GitHub", "close_title": "<PERSON><PERSON><PERSON>", "cancel_title": "Annuler", "send_magic_link": "Envoyer le lien", "sending": "Envoi en cours...", "magic_link_sent": "Lien magique envoyé ! Vérifiez votre email.", "magic_link_error": "Échec de l'envoi. Veuillez réessayer.", "invalid_email": "Veuillez saisir une adresse email valide", "something_wrong": "Une erreur s'est produite. Veuillez réessayer.", "check_email_title": "Vérifiez votre email", "check_email_description": "Nous avons envoyé un lien de connexion à votre email. Cliquez sur le lien pour vous connecter."}, "my_orders": {"title": "<PERSON><PERSON> commandes", "description": "Commandes payées avec ShipAny.", "no_orders": "Aucune commande trouvée", "tip": "", "activate_order": "<PERSON><PERSON> la commande", "actived": "Activé", "join_discord": "Rejoin<PERSON>", "read_docs": "Lire la documentation", "table": {"order_no": "N° de commande", "email": "Email", "product_name": "Nom du produit", "amount": "<PERSON><PERSON>", "paid_at": "<PERSON><PERSON> le", "github_username": "Nom d'utilisateur GitHub", "status": "Statut"}}, "credits": {"remaining": ""}, "my_credits": {"title": "<PERSON>s crédits", "left_tip": "Crédits restants : {left_credits}", "no_credits": "Aucun enregistrement de crédits", "recharge": "Recharger", "table": {"trans_no": "N° de transaction", "trans_type": "Type de transaction", "credits": "Crédits", "updated_at": "Mis à jour le", "status": "Statut"}}, "api_keys": {"title": "Clés API", "tip": "Gardez vos clés API en sécurité pour éviter les fuites", "no_api_keys": "Aucune clé API", "create_api_key": "<PERSON><PERSON><PERSON> une clé API", "table": {"name": "Nom", "key": "Clé", "created_at": "<PERSON><PERSON><PERSON>"}, "form": {"name": "Nom", "name_placeholder": "Nom de la clé API", "submit": "Envoyer"}}, "blog": {"title": "Blog", "description": "Actualités, ressources et mises à jour sur ShipAny", "read_more_text": "Lire plus"}, "my_invites": {"title": "Mes invitations", "description": "Voir vos enregistrements d'invitations", "no_invites": "Aucun enregistrement d'invitation trouvé", "my_invite_link": "Mon lien d'invitation", "edit_invite_link": "Modifier le lien d'invitation", "copy_invite_link": "Copier le lien d'invitation", "invite_code": "Code d'invitation", "invite_tip": "Invitez 1 ami à acheter ShipAny, gagnez 50 $ de récompense.", "invite_balance": "Solde des récompenses d'invitation", "total_invite_count": "Nombre total d'invitations", "total_paid_count": "Nombre total de payants", "total_award_amount": "Montant total des récompenses", "update_invite_code": "Définir le code d'invitation", "update_invite_code_tip": "Saisissez votre code d'invitation personnalisé", "update_invite_button": "Enregistrer", "no_orders": "Vous ne pouvez pas inviter d'autres personnes avant d'avoir acheté ShipAny", "no_affiliates": "Vous n'êtes pas autorisé à inviter d'autres personnes, veuillez nous contacter pour obtenir l'autorisation.", "table": {"invite_time": "Heure d'invitation", "invite_user": "Utilisateur invité", "status": "Statut", "reward_percent": "Pourcentage de récompense", "reward_amount": "<PERSON><PERSON> récompense", "pending": "En attente", "completed": "<PERSON><PERSON><PERSON><PERSON>"}}, "feedback": {"title": "Commentaires", "description": "Nous aimerions savoir ce qui s'est bien passé ou comment nous pouvons améliorer l'expérience produit.", "submit": "Envoyer", "loading": "Envoi en cours...", "contact_tip": "Autres moyens de nous contacter", "rating_tip": "Que pensez-vous de ShipAny ?", "placeholder": "Laissez vos commentaires ici..."}, "my_creations": {"title": "Mes créations", "description": "Voir et gérer toutes vos esquisses générées par IA", "metadata": {"title": "Mes Œuvres d'Art IA - Voir et Gérer les Images Générées | ImageToSketch.ai", "description": "Visualise<PERSON>, gérez et téléchargez toutes vos esquisses et œuvres d'art générées par IA. Accédez à votre galerie complète d'images transformées avec divers styles artistiques.", "keywords": "mon art IA, esquisses gén<PERSON>, galerie art IA, gérer créations, télécharger images IA, collection esquisses, historique art IA"}, "filters": {"all_styles": "Tous les styles", "all_status": "Tous les statuts", "styles": {"pencil": "Crayon", "charcoal": "<PERSON><PERSON><PERSON>", "sketcher": "<PERSON><PERSON><PERSON><PERSON>", "comicnoir": "Bande dessinée noir", "crosshatch": "Hachures croisées", "colorsketch": "<PERSON><PERSON><PERSON><PERSON> couleur", "crayon": "Crayon de couleur", "penart": "Art à l'encre", "inkwash": "<PERSON><PERSON> d'encre", "graphicnovel": "Roman graphique"}, "status": {"completed": "<PERSON><PERSON><PERSON><PERSON>", "generating": "Génération", "error": "<PERSON><PERSON><PERSON>"}}, "actions": {"load_more": "Charger plus", "delete_image": "Supprimer l'image", "click_to_preview": "Cliquer pour prévisualiser", "download": "Télécharger", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>"}, "preview": {"title": "Esquisse style {style}", "close_hint": "Cliquez à l'extérieur pour fermer"}, "delete_confirm": {"title": "Supprimer l'image", "message": "Êtes-vous sûr de vouloir supprimer cette image ? Cette action ne peut pas être annulée.", "image_alt": "Image à supprimer"}, "empty": {"message": "Aucune création trouvée"}, "loading": "Chargement..."}, "imagetoimage": {"name": "image vers image", "title": "Générateur IA de transformation d'images", "description": "Exploitez l'IA pour transformer, styliser et recréer n'importe quelle image sans effort", "upload": {"title": "Téléchargement d'image", "description": "Téléchargez une image à utiliser comme référence, maximum 5 images autorisées.", "button": "Télécharger l'image", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 Mo", "uploadedLabel": "Images téléchargées"}, "prompt": {"title": "Invite", "description": "Décrivez ce que vous voulez changer dans l'image", "placeholder": "Exemple : Changer l'arrière-plan en ciel bleu.", "maxLength": "1000"}, "aspectRatio": {"title": "Rapport d'aspect", "description": "Choisissez le rapport d'aspect que vous souhaitez utiliser", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "outputs": {"title": "Nombre de sorties", "description": "Choisissez le nombre de sorties que vous souhaitez générer"}, "generate": {"button": "Générer des images", "generating": "Génération...", "icon": "✨"}, "preview": {"empty": {"title": "Téléchargez une image pour commencer", "description": "Téléchargez une image et décrivez les changements que vous souhaitez voir pour générer des résultats époustouflants"}, "navigation": {"of": "sur"}, "download": "Télécharger l'image", "transformLabel": "Transformé en style Ghibli"}, "messages": {"maxImagesError": "Maximum {max} images autorisées", "fileTooLarge": "Le fichier {name} est trop volumineux. La taille maximale est de {size}Mo", "formatNotSupported": "Le format du fichier {name} n'est pas pris en charge", "uploadSuccess": "{count} image(s) téléchargée(s) avec succès", "noImages": "Veuillez télécharger au moins une image", "noPrompt": "<PERSON>eu<PERSON>z saisir une invite", "generateSuccess": "Images générées avec succès !", "generateError": "Échec de la génération des images. Veuillez réessayer."}}, "sketchtoimage": {"name": "esquisse vers image", "title": "Convertisseur IA Esquisse vers Image Réaliste", "description": "Transformez vos esquisses et dessins en images photoréalistes avec une technologie IA avancée", "disabled": false, "header": {"title": "IA Esquisse vers Image", "description": "Transformez vos esquisses en images réalistes avec l'IA"}, "upload": {"title": "Téléchargement d'esquisse", "description": "Téléchargez votre esquisse ou dessin pour le transformer en image réaliste, maximum 3 esquisses autorisées.", "button": "Télécharger l'esquisse", "formats": ".jpg / .png / .webp", "maxSize": "≤ 20 Mo", "uploadedLabel": "<PERSON><PERSON><PERSON><PERSON>", "file_types": "JPG, PNG, WebP", "max_size": "Max {maxSize}Mo", "uploaded_count": "<PERSON><PERSON><PERSON>chargé ({count}/{max})", "add_more": "Ajouter plus", "max_images_error": "Maximum {max} esquisses autorisées", "invalid_file_type": "{filename} n'est pas un fichier image valide", "file_size_error": "La taille du fichier dépasse {maxSize}Mo", "success": "{filename} téléchargé avec succès", "failed": "Échec du téléchargement de {filename}"}, "style": {"title": "Choi<PERSON> le style", "description": "Sélectionnez le style artistique pour vos images générées", "more_styles": "Plus de styles", "all_styles": "Tous les styles", "style": "Style", "options": {"architecture": {"label": "Architecture", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/architecture.png", "description": "Style de dessin architectural et technique"}, "default": {"label": "<PERSON><PERSON> <PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/default.png", "description": "Conversion réaliste standard"}, "photorealistic": {"label": "Photoréaliste", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/Photorealistic.png", "description": "Résultats ultra-réalistes de type photo"}, "digitalart": {"label": "Art numérique", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/digitalart.png", "description": "Style d'art numérique moderne"}, "anime": {"label": "Anime", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/anime.png", "description": "Style d'illustration anime japonais"}, "interiordesign": {"label": "Design d'intérieur", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/interiordesign.png", "description": "Visualisation de design d'intérieur"}, "3d": {"label": "3D", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/3d.png", "description": "Rendu tridimensionnel"}, "pixar": {"label": "Pixar", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/pixar.png", "description": "Style d'animation Pixar"}, "fantasy": {"label": "Fantasy", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/fantasy.png", "description": "Art fantastique et thèmes magiques"}, "rpg": {"label": "JDR", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/rpg.png", "description": "Art de personnages de jeux de rôle"}, "comicbook": {"label": "Bande dessinée", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/comicbook.png", "description": "Style d'illustration de bande dessinée"}, "clay": {"label": "Argile", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/clay.png", "description": "Sculpture et modelage d'argile"}, "vectorart": {"label": "Art vectoriel", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/vectorart.png", "description": "Style d'illustration vectorielle propre"}, "minimalist": {"label": "Minimaliste", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/minimalist.png", "description": "Design simple et épuré"}, "watercolor": {"label": "<PERSON><PERSON><PERSON><PERSON>", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/watercolor.png", "description": "Technique de peinture à l'aquarelle"}, "oilpainting": {"label": "Peinture à l'huile", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/oilpainting.png", "description": "Style de peinture à l'huile classique"}, "gta": {"label": "GTA", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/gta.png", "description": "Style de jeu Grand Theft Auto"}, "minecraft": {"label": "Minecraft", "previewImage": "https://r2.imagetosketch.ai/sketch-to-image/styles/minecraft.png", "description": "Style de blocs Minecraft"}}}, "creativity": {"title": "(Optionnel) Ajuster la créativité", "description": "Contrôlez à quel point l'IA doit être créative avec votre esquisse", "similar": "<PERSON><PERSON><PERSON><PERSON>", "creative": "<PERSON><PERSON><PERSON><PERSON>", "current": "Actuel", "balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slider_description": "<PERSON><PERSON><PERSON><PERSON> le curseur pour ajuster le niveau de créativité"}, "aspectRatio": {"title": "Rapport d'aspect", "description": "Choisissez le rapport d'aspect pour votre image générée", "ratio": "Rapport", "options": {"1:1": "1:1", "3:2": "3:2", "2:3": "2:3"}}, "generate": {"button": "Générer l'image", "generating": "Génération...", "icon": "🎨", "button_with_credits": "G<PERSON><PERSON>rer l'image ({credits} crédits)", "insufficient_credits": "Crédits insuffisants", "need_credits": "Besoin de {credits} crédits", "no_images_error": "Veuillez télécharger au moins une esquisse", "insufficient_credits_error": "Crédits insuffisants, veuillez recharger", "success": "Image générée avec succès !", "failed": "Échec de la génération, veuillez réessayer", "network_error": "Échec de la génération, veuillez vérifier la connexion réseau", "server_busy": "Le serveur est occupé, veuil<PERSON>z réessayer plus tard", "regenerate_success": "Regénération réussie !", "regenerate_failed": "Échec de la regénération", "regenerate_network_error": "Échec de la regénération, veuillez réessayer"}, "delete_task": {"success": "Tâche supprimée", "failed": "Échec de la suppression"}, "download": {"success": "Image téléchargée avec succès !", "trying_to_download": "Tentative de téléchargement de l'image...", "cannot_direct_download": "Impossible de télécharger directement, veuillez faire un clic droit pour enregistrer", "link_copied": "Lien de l'image copié dans le presse-papiers", "failed": "Échec du téléchargement"}, "time": {"just_now": "À l'instant", "minutes_ago": "Il y a {minutes} minutes", "hours_ago": "Il y a {hours} heures", "days_ago": "Il y a {days} jours"}, "preview": {"empty": {"title": "Téléchargez une esquisse pour commencer", "description": "Téléchargez votre esquisse et sélectionnez un style pour générer des images réalistes époustouflantes"}, "navigation": {"of": "sur"}, "download": "Télécharger l'image", "transformLabel": "Transformé en image réaliste", "generated_sketch": "<PERSON><PERSON><PERSON><PERSON> ", "modal": {"title": "Aperçu de l'image", "close": "<PERSON><PERSON><PERSON>", "download": "Télécharger", "close_hint": "Cliquez à l'extérieur ou appuyez sur ESC pour fermer", "downloadFilename": "image_générée_{timestamp}.png"}}, "actions": {"add_more": "Ajouter plus", "add": "Ajouter", "preview": "<PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger", "retry": "<PERSON><PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON><PERSON><PERSON>", "open_image": "<PERSON>u<PERSON><PERSON>r l'image", "close": "<PERSON><PERSON><PERSON>", "test_url": "Tester l'URL"}, "status": {"generating": "Génération", "completed": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>"}, "alt_text": {"generated_image": "Image réaliste générée", "uploaded_sketch": "<PERSON><PERSON><PERSON><PERSON>", "generated_sketch": "<PERSON><PERSON><PERSON><PERSON>"}, "tooltips": {"preview_image": "Aperçu de l'image", "download_image": "Télécharger l'image", "delete_image": "Supprimer l'image"}, "errors": {"failed_to_load": "Échec du chargement"}, "messages": {"maxImagesError": "Maximum {max} esquisses autorisées", "fileTooLarge": "Le fichier {name} dépasse la limite de taille de {size}Mo", "formatNotSupported": "Le format du fichier {name} n'est pas pris en charge", "uploadSuccess": "{count} esquisses té<PERSON>cha<PERSON><PERSON> avec succès", "noImages": "Veuillez télécharger au moins une esquisse", "generateSuccess": "Image générée avec succès", "generateError": "Échec de la génération d'image"}, "examples": {"panel_title": "Exemples de résultats", "recent_tasks_title": "Tâches récentes", "view_all": "Voir tout", "no_tasks": "Aucune tâche pour le moment", "no_tasks_description": "G<PERSON><PERSON>rez votre première esquisse pour la voir ici", "loading_history": "Chargement de l'historique...", "cta": {"title": "Voulez-vous convertir vos propres esquisses ?", "button": "Connectez-vous pour commencer"}, "items": [{"url": "/imgs/sketchtoimage/examples/example1.png", "title": "Génération de portraits", "description": "Transformer les esquisses de portraits en photos réalistes"}, {"url": "/imgs/sketchtoimage/examples/example2.png", "title": "Création de paysages", "description": "Convertir les dessins de paysages en superbes photos"}, {"url": "/imgs/sketchtoimage/examples/example3.png", "title": "Visualisation architecturale", "description": "Transformer les esquisses architecturales en rendus réalistes"}]}}}