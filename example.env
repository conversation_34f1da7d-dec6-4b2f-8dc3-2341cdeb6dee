# 复制此文件为 .env.development 并填入实际的配置值

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# OAuth Providers
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true

# Email Provider (Magic Link) - 使用 Resend SMTP
NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true
RESEND_API_KEY=your-resend-api-key
NEXT_PUBLIC_EMAIL_FROM=<EMAIL>

# 如果你没有域名，可以使用 Resend 的测试邮箱
# NEXT_PUBLIC_EMAIL_FROM=<EMAIL>

# 其他配置
NEXT_PUBLIC_WEB_URL=http://localhost:3000
NEXT_PUBLIC_PROJECT_NAME=ImageToSketch

# 使用步骤:
# 1. 注册 https://resend.com/ 获取 API Key
# 2. 配置发送邮箱域名或使用测试邮箱
# 3. 重启开发服务器
# 4. 测试邮箱登录功能 