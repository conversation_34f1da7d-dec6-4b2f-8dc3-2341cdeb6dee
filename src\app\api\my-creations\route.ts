import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getUserCreationsOptimized } from '@/models/sketch';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    const userUuid = session?.user?.uuid;

    if (!userUuid) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const style = searchParams.get('style') || undefined;
    const status = searchParams.get('status') || undefined;
    
    console.log('Fetching user creations with optimized query:', { userUuid, page, limit, style, status });
    
    // 使用优化后的查询获取创作数据
    const creationsData = await getUserCreationsOptimized(userUuid, {
      page,
      limit,
      style,
      status,
    });

    console.log('Optimized creations query completed:', {
      creationsCount: creationsData.creations.length,
      total: creationsData.total,
      hasMore: creationsData.hasMore
    });

    return NextResponse.json({
      success: true,
      data: creationsData
    });

  } catch (error) {
    console.error('Error fetching user creations:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
} 