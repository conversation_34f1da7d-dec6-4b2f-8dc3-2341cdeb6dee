// app/(main)/image-to-image/page.tsx
"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import clsx from "clsx";
import {
  Upload,
  ChevronLeft,
  ChevronRight,
  X,
  Sparkles,
  Loader2,
  Download,
  Check,
  ChevronDown,
  ChevronUp,
  Plus,
  RefreshCw,
  AlertCircle,
  Clock,
  Trash2,
  ChevronRight as MoreIcon,
} from "lucide-react";
import { toast } from "sonner";
import ImageToSketchBg from "./bg";
import Image from 'next/image';
import { useAppContext } from "@/contexts/app";
import { useRouter } from "next/navigation";
import { LandingPage } from "@/types/pages/landing";
import imageCompression from 'browser-image-compression';
import { useTranslations } from "next-intl";

// 提取 ImageToSketchSection 类型
type ImageToSketchSection = NonNullable<LandingPage['imagetosketch']>;

type SketchStyle = 
  | "architecture"
  | "default" 
  | "photorealistic"
  | "digitalart"
  | "anime"
  | "interiordesign"
  | "3d"
  | "pixar"
  | "fantasy"
  | "rpg"
  | "comicbook"
  | "clay"
  | "vectorart"
  | "minimalist"
  | "watercolor"
  | "oilpainting"
  | "gta"
  | "minecraft";

type Aspect = "1:1" | "3:2" | "2:3";

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
}

// 任务状态类型
interface Task {
  id: string;
  timestamp: Date;
  status: 'generating' | 'completed' | 'error';
  style: SketchStyle;
  aspectRatio: Aspect;
  creativity?: number; // 新增创意度参数
  originalImages: Array<{
    id: string;
    url: string;
    name: string;
  }>;
  results: Array<{
    id: string;
    originalImageId: string;
    url: string;
    original_image_url?: string;
    status: 'generating' | 'completed' | 'error';
    progress: number;
  }>;
  isLocal?: boolean;    // 是否为本地生成中的任务
  isHistory?: boolean;  // 是否为历史任务
}

// 预览模态框状态
interface PreviewModal {
  isOpen: boolean;
  imageUrl: string;
  title: string;
}

// 新增样式选项配置
const styleOptions = {
  architecture: {
    label: "Architecture",
    description: "Architectural sketches and technical drawings",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/architecture.png"
  },
  default: {
    label: "Default",
    description: "Standard sketch style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/default.png"
  },
  photorealistic: {
    label: "Photorealistic",
    description: "Realistic photo-like results",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/Photorealistic.png"
  },
  digitalart: {
    label: "Digital Art",
    description: "Modern digital artwork style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/digitalart.png"
  },
  anime: {
    label: "Anime",
    description: "Japanese anime style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/anime.png"
  },
  interiordesign: {
    label: "Interior Design",
    description: "Interior design sketches",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/interiordesign.png"
  },
  "3d": {
    label: "3D",
    description: "Three-dimensional rendering",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/3d.png"
  },
  pixar: {
    label: "Pixar",
    description: "Pixar animation style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/pixar.png"
  },
  fantasy: {
    label: "Fantasy",
    description: "Fantasy art style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/fantasy.png"
  },
  rpg: {
    label: "RPG",
    description: "Role-playing game art",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/rpg.png"
  },
  comicbook: {
    label: "ComicBook",
    description: "Comic book style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/comicbook.png"
  },
  clay: {
    label: "Clay",
    description: "Clay sculpture style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/clay.png"
  },
  vectorart: {
    label: "Vector art",
    description: "Vector illustration style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/vectorart.png"
  },
  minimalist: {
    label: "Minimalist",
    description: "Minimalist design",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/minimalist.png"
  },
  watercolor: {
    label: "WaterColor",
    description: "Watercolor painting style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/watercolor.png"
  },
  oilpainting: {
    label: "Oil Painting",
    description: "Oil painting technique",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/oilpainting.png"
  },
  gta: {
    label: "GTA",
    description: "Grand Theft Auto style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/gta.png"
  },
  minecraft: {
    label: "Minecraft",
    description: "Minecraft block style",
    previewImage: "https://r2.imagetosketch.ai/sketch-to-image/styles/minecraft.png"
  }
};

export default function SketchToImage({ section }: { section: ImageToSketchSection }) {
  const { user, setUser, setShowSignModal } = useAppContext();
  const router = useRouter();
  const t = useTranslations();
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<SketchStyle>("default");
  const [selectedAspect, setSelectedAspect] = useState<Aspect>("1:1");
  const [creativity, setCreativity] = useState(50); // 新增创意度状态，默认值50（中间位置）
  const [isGenerating, setIsGenerating] = useState(false);
  const [historyTasks, setHistoryTasks] = useState<Task[]>([]); // 历史任务
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [previewModal, setPreviewModal] = useState<PreviewModal>({
    isOpen: false,
    imageUrl: "",
    title: ""
  });
  const [currentExampleIndex, setCurrentExampleIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // 新增侧边栏状态和展开状态
  const [isStyleSidebarOpen, setIsStyleSidebarOpen] = useState(false);
  const [isStyleSectionExpanded, setIsStyleSectionExpanded] = useState(false);
  
  // Calculate credits needed
  const creditsNeeded = uploadedImages.length;
  const userCredits = user?.credits?.left_credits || 0;
  const hasEnoughCredits = userCredits >= creditsNeeded;

  const [styleImagesLoaded, setStyleImagesLoaded] = useState(false);


  
  // 组件加载时的调试信息


  // 添加删除任务标记到localStorage
  const markTaskAsDeleted = (taskId: string) => {
    const deletedTasks = JSON.parse(localStorage.getItem('deleted_tasks') || '[]');
    deletedTasks.push(taskId);
    localStorage.setItem('deleted_tasks', JSON.stringify(deletedTasks));

  };

  // 检查任务是否已被删除
  const isTaskDeleted = (taskId: string) => {
    const deletedTasks = JSON.parse(localStorage.getItem('deleted_tasks') || '[]');
    return deletedTasks.includes(taskId);
  };

  // 清理已删除任务的标记
  const cleanDeletedTaskMarks = () => {
    localStorage.removeItem('deleted_tasks');
  };

  // Example images from configuration
  const exampleImages = section.examples?.items || [
    {
      url: "/imgs/sketchtoimage/before.png",
      title: "Sketch to Photo Style",
      description: "Transform your sketches into realistic images"
    },
    {
      url: "/imgs/sketchtoimage/after.png", 
      title: "Digital Art Style",
      description: "Create digital artwork from your drawings"
    }
  ];
  
  // 添加URL格式化函数
  const formatImageUrl = (url: any): string => {
    // 检查参数是否为有效的字符串
    if (!url || typeof url !== 'string') {
      console.warn('formatImageUrl received invalid URL:', url);
      return '';
    }
    
    const trimmedUrl = url.trim();

    
    // 如果已经有协议，直接返回
    if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
      return trimmedUrl;
    }
    
    // 处理 imagetosketch.ai R2 域名
    if (trimmedUrl.startsWith('r2.imagetosketch.ai')) {
      return `https://${trimmedUrl}`;
    }
    
    // 如果是 /r2.imagetosketch.ai 开头（被当作相对路径）
    if (trimmedUrl.startsWith('/r2.imagetosketch.ai')) {
      return `https://${trimmedUrl.substring(1)}`;
    }
    
    // 兼容旧的 erniex1.org 域名
    if (trimmedUrl.startsWith('erniex1.org')) {
      return `https://${trimmedUrl}`;
    }
    
    if (trimmedUrl.startsWith('/erniex1.org')) {
      return `https://${trimmedUrl.substring(1)}`;
    }
    
    // 如果包含imagetosketch.ai但没有协议前缀
    if (trimmedUrl.includes('imagetosketch.ai') && !trimmedUrl.startsWith('https://')) {
      return `https://${trimmedUrl}`;
    }
    

    return trimmedUrl;
  };

  // 添加图片加载错误处理
  const [imageLoadErrors, setImageLoadErrors] = useState<Set<string>>(new Set());

  const handleImageError = (resultId: string, originalUrl: string) => {
    console.error(`Failed to load image: ${originalUrl}`);
    setImageLoadErrors(prev => new Set([...prev, resultId]));
  };

  const handleImageLoad = (resultId: string) => {
    setImageLoadErrors(prev => {
      const newSet = new Set(prev);
      newSet.delete(resultId);
      return newSet;
    });
  };

  const maxImages = 3;
  const maxFileSize = 20; // MB

  // 使用专业库进行图片压缩
  const compressImage = async (file: File): Promise<File> => {
    const options = {
      maxSizeMB: 1, // 最大1MB
      maxWidthOrHeight: 1024, // 最大宽度或高度
      useWebWorker: true, // 使用Web Worker提升性能
      fileType: file.type,      // 保持原始文件类型
      initialQuality: 0.8,      // 初始质量
      alwaysKeepResolution: true, // 保持分辨率
    };
    
    try {
      const compressedFile = await imageCompression(file, options);

      return compressedFile;
    } catch (error) {
      console.error('图片压缩失败:', error);
      return file; // 压缩失败时返回原文件
    }
  };

  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (!files.length) return;

    // 检查文件数量限制
    if (uploadedImages.length + files.length > maxImages) {
      toast.error(section.messages?.maxImagesError?.replace('{max}', maxImages.toString()) || `Max ${maxImages} images allowed`);
      return;
    }

    // 验证每个文件
    const validFiles = files.filter(file => {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        toast.error(section.messages?.formatNotSupported?.replace('{name}', file.name) || `File ${file.name} is not a valid image format`);
        return false;
      }

      // 检查文件大小 (20MB)
      const maxSizeBytes = maxFileSize * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        toast.error(section.messages?.fileTooLarge?.replace('{name}', file.name).replace('{size}', maxFileSize.toString()) || `File ${file.name} exceeds ${maxFileSize}MB size limit`);
        return false;
      }

      return true;
    });

    if (validFiles.length === 0) return;

    // 创建新的上传图片对象
    const newImages: UploadedImage[] = validFiles.map(file => ({
      id: `img_${Date.now()}_${Math.random()}`,
      file,
      url: URL.createObjectURL(file),
      name: file.name
    }));

    // 添加到已上传图片列表
    setUploadedImages(prev => [...prev, ...newImages]);
    
    // 显示成功提示 - 使用英文文本避免多语言问题
    toast.success(`Successfully uploaded ${newImages.length} ${newImages.length === 1 ? 'image' : 'images'}`);

    // 清空input值，允许重复上传同一文件
    e.target.value = '';
  };

  // Convert URL to File object
  const urlToFile = async (url: string, filename: string): Promise<File> => {
    try {
      const formattedUrl = formatImageUrl(url);
      const response = await fetch(formattedUrl);
      const blob = await response.blob();
      
      // 创建File对象
      const file = new File([blob], filename, { 
        type: blob.type || 'image/jpeg',
        lastModified: Date.now()
      });
      
      return file;
    } catch (error) {
      console.error('Failed to convert URL to File:', error);
      throw error;
    }
  };

  // Convert image to base64
  const imageToBase64 = async (file: File): Promise<string> => {
    // 检查是否在浏览器环境
    if (typeof window === 'undefined') {
      throw new Error('imageToBase64 can only be called in browser environment');
    }

    // 先压缩图片
    const compressedFile = await compressImage(file);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = () => reject(new Error('FileReader error'));
      reader.readAsDataURL(compressedFile);
    });
  };

  const handleGenerate = async () => {
    if (!uploadedImages.length) {
      toast.error("Please upload at least one sketch");
      return;
    }

    if (!user) {
      setShowSignModal(true);
      return;
    }

    if (!hasEnoughCredits) {
      toast.error("Insufficient credits, please top up");
      return;
    }

    setIsGenerating(true);

    // Create a local task immediately to show in recent tasks
    const localTaskId = `local_${Date.now()}`;
    const localTask: Task = {
      id: localTaskId,
      timestamp: new Date(),
      status: 'generating',
      style: selectedStyle,
      aspectRatio: selectedAspect,
      creativity,
      originalImages: uploadedImages.map(img => ({
        id: img.id,
        url: img.url,
        name: img.name
      })),
      results: uploadedImages.map(img => ({
        id: `result_${img.id}`,
        originalImageId: img.id,
        url: '',
        status: 'generating',
        progress: 0,
      })),
      isLocal: true,
      isHistory: false,
    };

    // Add local task to history immediately
    setHistoryTasks(prev => [localTask, ...prev]);

    try {
      // Convert images to base64
      const imagePromises = uploadedImages.map(async (img) => ({
        id: img.id,
        data: await imageToBase64(img.file),
        filename: img.name
      }));

      const imageData = await Promise.all(imagePromises);

      // Make API call - 使用sketch-to-image接口
      const response = await fetch('/api/image-gen/sketch-to-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          images: imageData,
          style: selectedStyle,
          aspectRatio: selectedAspect,
          creativity, // 传递创意度参数
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Don't remove local task immediately - let refreshHistory handle it
        // This prevents the task from disappearing
        
        // Refresh user credits
        await refreshUserCredits();
        
        // Clear uploaded images after successful generation
        setUploadedImages([]);
        
        // 优化后的刷新逻辑 - 延长延迟时间，减少API调用频率
        setTimeout(() => {
          // 清除缓存，确保获取最新数据
          localStorage.removeItem(HISTORY_CACHE_KEY);
          // 强制刷新历史任务
          loadHistoryTasks(true, 'generation-complete');
        }, 2000); // 延长到2秒，给服务器更多时间处理
        
        toast.success("Generation completed successfully");
      } else {
        // Remove local task and show error
        setHistoryTasks(prev => prev.filter(task => task.id !== localTaskId));
        
        // Handle error - show user-friendly message
        if (data.error && data.error.includes('busy')) {
          toast.error("Server is busy, please try again later");
        } else {
          toast.error(data.message || "Generation failed");
        }
      }

    } catch (error) {
      console.error('Generation error:', error);
      
      // Remove local task on error
      setHistoryTasks(prev => prev.filter(task => task.id !== localTaskId));
      
      // Check if it's a network error or server overload
      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast.error("Network error, please check your connection");
      } else {
        toast.error("Server is busy, please try again later");
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // 重新生成功能
  const handleRegenerate = async (task: Task) => {
    try {
      // 检查用户登录状态
      if (!user) {
        setShowSignModal(true);
        return;
      }

      // 检查积分是否足够
      const requiredCredits = task.results?.length || 1;
      if (userCredits < requiredCredits) {
        toast.error('Insufficient credits, please top up');
        return;
      }

      // 检查任务是否有有效的原始图片URL
      if (!task.results || task.results.length === 0) {
        toast.error('No original images found for regeneration');
        return;
      }

      // 检查是否有任何结果包含原始图片URL
      const validResults = task.results.filter(result => result.original_image_url);
      if (validResults.length === 0) {
        toast.error('No original images found for regeneration');
        return;
      }



      // 使用原始图片URL创建File对象
      const originalImagePromises = validResults.map(async (result: any, index: number) => {
        console.log(`Processing result ${index}:`, result);
        
        try {
          // 使用formatImageUrl确保URL格式正确（添加https://前缀）
          const imageUrl = formatImageUrl(result.original_image_url);
          
          console.log(`Fetching image ${index} from URL:`, imageUrl);
          const response = await fetch(imageUrl);
          if (!response.ok) {
            console.error(`Failed to fetch image ${index}:`, response.status, response.statusText);
            throw new Error(`Failed to fetch original image: ${response.status}`);
          }
          
          const blob = await response.blob();
          const filename = `original_${index}.${blob.type.split('/')[1] || 'jpg'}`;
          console.log(`Successfully loaded image ${index}, size:`, blob.size, 'bytes');
          
          return new File([blob], filename, { type: blob.type });
        } catch (error) {
          console.error(`Error loading original image ${index}:`, error);
          throw error;
        }
      });

      // 等待所有图片加载完成
      const originalImages = await Promise.all(originalImagePromises);
      
      // 转换为UploadedImage格式
      const newUploadedImages: UploadedImage[] = originalImages.map((file, index) => ({
        id: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        file,
        url: URL.createObjectURL(file),
        name: file.name
      }));

      // 更新上传的图片
      setUploadedImages(newUploadedImages);
      
      // 恢复原始参数
      setSelectedStyle(task.style);
      setSelectedAspect(task.aspectRatio);
      if (task.creativity !== undefined) {
        setCreativity(task.creativity);
      }
      
      // 显示成功消息
      toast.success('Images loaded for regeneration');
      
      // 创建本地任务以实时显示进度
      const taskId = `regenerate_${Date.now()}`;
      const newTask: Task = {
        id: taskId,
        timestamp: new Date(),
        status: 'generating',
        style: task.style,
        aspectRatio: task.aspectRatio,
        creativity: task.creativity,
        originalImages: newUploadedImages.map(img => ({
          id: img.id,
          url: img.url,
          name: img.name
        })),
        results: newUploadedImages.map(img => ({
          id: `result_${img.id}`,
          originalImageId: img.id,
          url: '',
          status: 'generating',
          progress: 0
        })),
        isLocal: true,
        isHistory: false
      };

      // 添加到本地任务列表
      setHistoryTasks(prev => [newTask, ...prev]);
      
             // 创建一个内部生成函数，避免状态时序问题
       const performRegeneration = async () => {
        if (!newUploadedImages.length) {
          console.error('No uploaded images for regeneration');
          toast.error('Please upload at least one image');
          return;
        }

        if (!user) {
          setShowSignModal(true);
          return;
        }

        if (userCredits < newUploadedImages.length) {
          toast.error('Insufficient credits');
          return;
        }

        setIsGenerating(true);

        try {
          console.log('Starting regeneration with images:', newUploadedImages.length);
          
          // Convert images to base64
          const base64Images = await Promise.all(
            newUploadedImages.map(img => imageToBase64(img.file))
          );

          console.log('All images converted to base64, count:', base64Images.length);

          // Make API call - 使用sketch-to-image接口
          const response = await fetch('/api/image-gen/sketch-to-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              images: base64Images,
              style: task.style,  // 使用原任务的样式
              aspectRatio: task.aspectRatio,  // 使用原任务的长宽比
              creativity: task.creativity || 50, // 使用原任务的创意度
              creditsNeeded: newUploadedImages.length, // 传递所需积分
            }),
          });

          const data = await response.json();
          console.log('API response for regeneration:', data);

          if (data.success && data.data?.results) {
            console.log('Regeneration successful');
            
            // 更新本地任务状态为完成
            setHistoryTasks(prev => prev.map(t => {
              if (t.id === taskId) {
                return {
                  ...t,
                  status: 'completed',
                  results: t.results.map((r, index) => ({
                    ...r,
                    url: data.data.results[index]?.imageUrl || `data:image/png;base64,${data.data.results[index]?.base64 || ''}`,
                    status: 'completed',
                    progress: 100
                  }))
                };
              }
              return t;
            }));
            
            // Clear uploaded images after successful generation
            setUploadedImages([]);
            
            // Refresh user credits
            await refreshUserCredits();
            
            // 优化后的刷新逻辑 - 延长延迟时间，减少API调用频率
            setTimeout(() => {
              // 清除缓存，确保获取最新数据
              localStorage.removeItem(HISTORY_CACHE_KEY);
              // 强制刷新历史任务
              loadHistoryTasks(true, 'regeneration-complete');
            }, 2000); // 延长到2秒，给服务器更多时间处理
            
            toast.success('Images generated successfully!');
          } else {
            console.error('Regeneration failed:', data);
            
            // 更新本地任务状态为错误
            setHistoryTasks(prev => prev.map(t => {
              if (t.id === taskId) {
                return {
                  ...t,
                  status: 'error',
                  results: t.results.map(r => ({
                    ...r,
                    status: 'error',
                    progress: 0
                  }))
                };
              }
              return t;
            }));
            
            toast.error(data.message || 'Generation failed');
          }

        } catch (error) {
          console.error('Regeneration error:', error);
          
          // 更新本地任务状态为错误
          setHistoryTasks(prev => prev.map(t => {
            if (t.id === taskId) {
              return {
                ...t,
                status: 'error',
                results: t.results.map(r => ({
                  ...r,
                  status: 'error',
                  progress: 0
                }))
              };
            }
            return t;
          }));
          
          toast.error('Failed to regenerate images');
        } finally {
          setIsGenerating(false);
        }
      };
      
      // 短暂延迟后调用重新生成，使用setTimeout确保状态更新完成
      setTimeout(() => {
        console.log('Starting regeneration process');
        performRegeneration();
      }, 500);

    } catch (error) {
      console.error('Error during regeneration:', error);
      toast.error('Regeneration failed, please try again');
    }
  };

  const deleteTask = async (taskId: string) => {
    try {
      // Remove from local state first
      setHistoryTasks(prev => prev.filter(task => task.id !== taskId));
      
      // Call API to delete from server
      const response = await fetch(`/api/sketch-tasks?id=${taskId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete API error:', {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`Failed to delete task: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      
      // If task was deleted successfully, update cache
      if (result.success) {
        // Clear cache to ensure fresh data on next load
        localStorage.removeItem(HISTORY_CACHE_KEY);
        
        // Update cache with current filtered data
        const currentTasks = getCachedHistory();
        if (currentTasks) {
          const filteredTasks = currentTasks.filter(task => task.id !== taskId);
          setCachedHistory(filteredTasks);
        }
        
        // Trigger event for other components
        const event = new CustomEvent('task-deleted', {
          detail: {
            taskId: taskId,
            taskType: 'sketch-to-image'
          }
        });
        window.dispatchEvent(event);
        
        toast.success("Task deleted successfully");
      } else {
        throw new Error(result.error || 'Failed to delete task');
      }
    } catch (error) {
      console.error('Delete error:', error);
      // Restore the task in local state if API call failed
      refreshHistory();
      toast.error("Failed to delete task");
    }
  };

  const previewImage = (url: string, title: string) => {
    setPreviewModal({
      isOpen: true,
      imageUrl: url,
      title
    });
  };

  const downloadImage = async (url: string, filename: string) => {
    try {
      const formattedUrl = formatImageUrl(url);
      
      // 使用代理API获取图片，避免CORS问题
      const proxyUrl = `/api/proxy-download?url=${encodeURIComponent(formattedUrl)}`;
      
      console.log('下载图片:', proxyUrl);
      
      // 使用 fetch 获取图片
      const response = await fetch(proxyUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // 将响应转换为 blob
      const blob = await response.blob();
      
      // 创建 blob URL
      const blobUrl = URL.createObjectURL(blob);
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;
      link.style.display = 'none';
      
      // 执行下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理 blob URL
      setTimeout(() => {
        URL.revokeObjectURL(blobUrl);
      }, 100);
      
      console.log('下载成功');
      
    } catch (error) {
      console.error('下载失败:', error);
      // 如果下载失败，打开新窗口作为备选
      window.open(formatImageUrl(url), '_blank');
    }
  };

  // 最后的fallback：引导用户手动下载
  const handleDownloadFallback = (url: string, filename: string) => {
    // 创建一个友好的提示
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black/80 z-[9999] flex items-center justify-center p-4';
    const downloadTitle = section.errors?.download_failed_title || "Download Instructions";
    const downloadMessage = section.errors?.download_failed_message || "Automatic download failed. Please follow these steps:";
    const downloadSteps = section.errors?.download_steps || [
      'Click "Open Image" below',
      'Right-click on the image',
      'Select "Save image as..."',
      `Save as "${filename}"`
    ];
    const openImageText = section.actions?.open_image || "Open Image";
    const closeText = section.actions?.close || "Close";

    modal.innerHTML = `
      <div class="bg-white rounded-lg p-6 max-w-md">
        <div class="text-lg font-semibold mb-4">${downloadTitle}</div>
        <p class="text-gray-600 mb-4">
          ${downloadMessage}
        </p>
        <ol class="list-decimal list-inside text-sm text-gray-600 mb-4 space-y-1">
          ${downloadSteps.map(step => `<li>${step.replace('{filename}', filename)}</li>`).join('')}
        </ol>
        <div class="flex gap-3">
          <button onclick="window.open('${url}', '_blank')" 
                  class="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            ${openImageText}
          </button>
          <button onclick="this.closest('.fixed').remove()" 
                  class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
            ${closeText}
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // 自动移除
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 30000);
  };

  // Format time for display
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    
    const days = Math.floor(hours / 24);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'generating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'completed':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: Task['status']) => {
    switch (status) {
      case 'generating':
        return section.status?.generating || "Generating";
      case 'completed':
        return section.status?.completed || "Completed";
      case 'error':
        return section.status?.error || "Error";
      default:
        return status;
    }
  };

  // 获取可用的风格选项
  const availableStyles = Object.entries(styleOptions) as Array<[SketchStyle, { label: string; previewImage: string; description: string }]>;
  const selectedStyleOption = availableStyles.find(([style]) => style === selectedStyle)?.[1];

  // 获取前6个样式用于主显示区域
  const displayedStyles = availableStyles.slice(0, 5); // 前5个样式
  const remainingStyles = availableStyles.slice(5); // 剩余样式

  const handleStyleSelect = (styleKey: SketchStyle) => {
    setSelectedStyle(styleKey);
    setIsStyleSidebarOpen(false); // 选择后关闭侧边栏
  };

  // Function to refresh user credits
  const refreshUserCredits = async () => {
    try {
      const resp = await fetch("/api/get-user-info", {
        method: "POST",
      });

      if (resp.ok) {
        const { data } = await resp.json();
        if (data && setUser) {
          setUser(data);
        }
      }
    } catch (error) {
      console.error('Failed to refresh user credits:', error);
    }
  };

  // Function to navigate to my-creations page
  const handleViewAllCreations = () => {
    router.push('/my-creations');
  };

  // Example carousel navigation functions
  const nextExample = () => {
    setCurrentExampleIndex((prev) => (prev + 1) % exampleImages.length);
  };

  const prevExample = () => {
    setCurrentExampleIndex((prev) => (prev - 1 + exampleImages.length) % exampleImages.length);
  };

  // 计算显示的任务（只显示sketch to image相关的历史任务）
  const displayTasks = historyTasks
    .filter(task => {
      // 只显示sketch to image相关的任务，可以通过任务类型或其他标识来判断
      // 这里暂时显示所有任务，您可以根据需要添加过滤条件
      return true;
    })
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 10);

  // 添加图片预加载功能
  useEffect(() => {
    let cleanup: (() => void) | undefined;
    
    // 预加载所有风格图片
    const preloadStyleImages = () => {
      const preloadContainer = document.createElement('div');
      preloadContainer.style.position = 'absolute';
      preloadContainer.style.left = '-9999px';
      preloadContainer.style.top = '-9999px';
      preloadContainer.style.visibility = 'hidden';
      preloadContainer.setAttribute('aria-hidden', 'true');
      
      const styleOptionsList = Object.values(styleOptions);
      let loadedCount = 0;
      
      styleOptionsList.forEach(option => {
        const img = document.createElement('img');
        img.src = option.previewImage;
        img.loading = 'eager';
        img.decoding = 'async';
        
        const handleLoad = () => {
          loadedCount++;
          console.log(`Style image preloaded: ${option.label} (${loadedCount}/${styleOptionsList.length})`);
          
          // 所有图片加载完成
          if (loadedCount === styleOptionsList.length) {
            setStyleImagesLoaded(true);
            console.log('All style images preloaded successfully!');
          }
        };
        
        const handleError = () => {
          loadedCount++;
          console.warn(`Failed to preload style image: ${option.label}`);
          
          // 即使有错误也要检查是否所有图片都处理完了
          if (loadedCount === styleOptionsList.length) {
            setStyleImagesLoaded(true);
          }
        };
        
        img.onload = handleLoad;
        img.onerror = handleError;
        preloadContainer.appendChild(img);
      });
      
      document.body.appendChild(preloadContainer);
      
      // 清理函数
      return () => {
        if (document.body.contains(preloadContainer)) {
          document.body.removeChild(preloadContainer);
        }
      };
    };

    // 延迟100ms执行，确保DOM已经渲染
    const timer = setTimeout(() => {
      cleanup = preloadStyleImages();
    }, 100);
    
    return () => {
      clearTimeout(timer);
      cleanup?.();
    };
  }, []);

  // 添加历史数据缓存配置
  const HISTORY_CACHE_KEY = 'sketchtoimage_history_tasks';
  const CACHE_DURATION = 10 * 60 * 1000; // 延长缓存时间到10分钟
  const API_DEBOUNCE_DELAY = 1000; // API调用防抖延迟1秒

  // 添加防抖引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastApiCallRef = useRef<number>(0);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 检查缓存是否有效
  const getCachedHistory = (): Task[] | null => {
    try {
      const cached = localStorage.getItem(HISTORY_CACHE_KEY);
      if (!cached) return null;

      const { data, timestamp } = JSON.parse(cached);
      const now = Date.now();

      // 检查缓存是否过期
      if (now - timestamp > CACHE_DURATION) {
        localStorage.removeItem(HISTORY_CACHE_KEY);
        return null;
      }

      // 将timestamp字符串转换为Date对象
      return data.map((task: any) => ({
        ...task,
        timestamp: new Date(task.timestamp)
      }));
    } catch (error) {
      console.error('Failed to parse cached history:', error);
      localStorage.removeItem(HISTORY_CACHE_KEY);
      return null;
    }
  };

  // 缓存历史数据
  const setCachedHistory = (tasks: Task[]) => {
    try {
      const cacheData = {
        data: tasks,
        timestamp: Date.now()
      };
      localStorage.setItem(HISTORY_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Failed to cache history:', error);
    }
  };

  // 智能清除缓存 - 只在必要时清除
  const smartClearCache = (reason: string) => {
    const now = Date.now();
    // 如果距离上次API调用不到30秒，不清除缓存
    if (now - lastApiCallRef.current < 30000) {
      console.log(`Skipping cache clear for ${reason} - too recent API call`);
      return false;
    }
    console.log(`Clearing cache for reason: ${reason}`);
    localStorage.removeItem(HISTORY_CACHE_KEY);
    return true;
  };

  // 检查是否需要刷新数据
  const shouldRefreshData = (reason: string): boolean => {
    const cachedTasks = getCachedHistory();

    // 如果没有缓存，需要刷新
    if (!cachedTasks) {
      console.log(`Should refresh for ${reason}: No cache`);
      return true;
    }

    // 如果缓存为空，需要刷新
    if (cachedTasks.length === 0) {
      console.log(`Should refresh for ${reason}: Empty cache`);
      return true;
    }

    // 对于SketchToImage组件，由于没有本地tasks状态，
    // 主要依据缓存的有效性来判断是否需要刷新
    console.log(`Should NOT refresh for ${reason}: Valid cache exists`);
    return false;
  };

  // 防抖的API调用函数
  const debouncedLoadHistoryTasks = useCallback((forceRefresh = false, reason = 'unknown') => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 检查是否需要防抖
    const now = Date.now();
    const timeSinceLastCall = now - lastApiCallRef.current;

    // 如果距离上次调用不到1秒，使用防抖
    if (timeSinceLastCall < API_DEBOUNCE_DELAY && !forceRefresh) {
      console.log(`Debouncing API call for reason: ${reason}, time since last: ${timeSinceLastCall}ms`);
      debounceTimerRef.current = setTimeout(() => {
        loadHistoryTasksInternal(forceRefresh, reason);
      }, API_DEBOUNCE_DELAY - timeSinceLastCall);
      return;
    }

    // 立即执行
    loadHistoryTasksInternal(forceRefresh, reason);
  }, []);

  // 内部实际执行API调用的函数
  const loadHistoryTasksInternal = async (forceRefresh = false, reason = 'unknown') => {
    console.log(`Loading history tasks - forceRefresh: ${forceRefresh}, reason: ${reason}`);

    // 如果不是强制刷新，先检查缓存
    if (!forceRefresh) {
      const cachedTasks = getCachedHistory();
      if (cachedTasks) {
        console.log(`Using cached history tasks (${cachedTasks.length} tasks)`);
        // 过滤掉空任务和已删除的任务
        const validTasks = cachedTasks.filter(task => {
          // 检查是否被标记为删除
          if (isTaskDeleted(task.id)) {
            return false;
          }

          // 检查是否有有效结果
          return task.results && task.results.length > 0 &&
            task.results.some(result => result.url && result.url.trim() !== '');
        });
        setHistoryTasks(validTasks);
        return;
      }
    }

    // 记录API调用时间
    lastApiCallRef.current = Date.now();

    setIsLoadingHistory(true);
    try {
      console.log(`Making API call to /api/sketch-tasks for reason: ${reason}`);
      const response = await fetch('/api/sketch-tasks?limit=10&type=sketch-to-image');
      const data = await response.json();

      if (data.success) {
        // 将timestamp字符串转换为Date对象并过滤掉空任务
        const transformedTasks = data.data.tasks
          .map((task: any) => ({
            ...task,
            timestamp: new Date(task.timestamp)
          }))
          .filter((task: any) => {
            // 检查是否被标记为删除
            if (isTaskDeleted(task.id)) {
              return false;
            }

            // 过滤掉没有有效结果的任务
            if (!task.results || task.results.length === 0) {
              return false;
            }

            // 过滤掉所有结果都没有有效URL的任务
            const hasValidResults = task.results.some((result: any) =>
              result.url && result.url.trim() !== '' && result.status === 'completed'
            );

            return hasValidResults;
          });

        console.log(`API returned ${transformedTasks.length} valid tasks`);
        setHistoryTasks(transformedTasks);
        // 缓存过滤后的数据
        setCachedHistory(transformedTasks);

        // 清理旧的删除标记（如果任务真的从API中消失了）
        if (transformedTasks.length === 0) {
          cleanDeletedTaskMarks();
        }
      }
    } catch (error) {
      console.error('Failed to load history:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // 保持向后兼容的loadHistoryTasks函数
  const loadHistoryTasks = (forceRefresh = false, reason = 'legacy') => {
    debouncedLoadHistoryTasks(forceRefresh, reason);
  };

  // 添加手动刷新功能
  const refreshHistory = () => {
    console.log('Manual refresh triggered');
    // 清除所有缓存和删除标记
    localStorage.removeItem(HISTORY_CACHE_KEY);
    cleanDeletedTaskMarks();
    // 强制刷新
    loadHistoryTasks(true, 'manual-refresh');
  };

  // 优化后的用户登录状态变化处理
  useEffect(() => {
    if (user?.uuid) {
      console.log('User logged in, loading history tasks');
      // 首次加载时先尝试使用缓存，如果没有缓存再调用API
      loadHistoryTasks(false, 'user-login');

      // 延迟刷新改为更长时间，并且只在缓存为空时执行
      const timeoutId = setTimeout(() => {
        const cachedTasks = getCachedHistory();
        if (!cachedTasks || cachedTasks.length === 0) {
          console.log('No cached tasks found, force refreshing after delay');
          loadHistoryTasks(true, 'user-login-delayed');
        }
      }, 2000); // 延长到2秒，减少频繁调用

      return () => {
        clearTimeout(timeoutId);
      };
    } else {
      setHistoryTasks([]);
      // 清除缓存当用户登出时
      localStorage.removeItem(HISTORY_CACHE_KEY);
      cleanDeletedTaskMarks();
    }
  }, [user?.uuid]);

  // 优化后的页面事件监听器 - 合并重复逻辑并添加防抖
  useEffect(() => {
    if (!user?.uuid) return;

    // 统一的刷新处理函数，使用智能判断
    const handlePageRefresh = (reason: string) => {
      console.log(`Page refresh triggered: ${reason}`);

      // 使用智能刷新判断
      if (shouldRefreshData(reason)) {
        loadHistoryTasks(false, reason);
      }
    };

    // 页面可见性变化 - 只在页面从隐藏变为可见时刷新
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        handlePageRefresh('visibility-change');
      }
    };

    // 窗口获得焦点 - 添加防抖，避免频繁触发
    let focusTimeout: NodeJS.Timeout;
    const handleWindowFocus = () => {
      clearTimeout(focusTimeout);
      focusTimeout = setTimeout(() => {
        handlePageRefresh('window-focus');
      }, 1000); // 1秒防抖
    };

    // 页面显示事件 - 主要用于从浏览器缓存返回的情况
    const handlePageShow = (event: PageTransitionEvent) => {
      // 只在从缓存加载时刷新
      if (event.persisted) {
        handlePageRefresh('page-show-cached');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleWindowFocus);
    window.addEventListener('pageshow', handlePageShow);

    return () => {
      clearTimeout(focusTimeout);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
      window.removeEventListener('pageshow', handlePageShow);
    };
  }, [user?.uuid]);

  // 优化后的任务删除事件监听器
  useEffect(() => {
    const handleTaskDeleted = (event: CustomEvent) => {
      console.log('SketchToImage: Received task-deleted event:', event.detail);

      // 检查是否是 sketch-to-image 类型的任务
      if (event.detail?.taskType === 'sketch-to-image' || !event.detail?.taskType) {
        const taskId = event.detail?.taskId;
        if (taskId) {
          console.log('SketchToImage: Processing task deletion for sketch-to-image type:', taskId);

          // 标记任务为已删除
          markTaskAsDeleted(taskId);

          // 立即从本地状态中移除任务，无需等待API
          setHistoryTasks(prev => {
            const filtered = prev.filter(task => task.id !== taskId);
            console.log(`SketchToImage: Removed task ${taskId}, tasks: ${prev.length} -> ${filtered.length}`);
            return filtered;
          });

          // 智能清除缓存 - 只在必要时清除
          if (smartClearCache('task-deleted')) {
            // 延迟刷新，给用户界面更新时间
            setTimeout(() => {
              loadHistoryTasks(false, 'task-deleted-refresh');
            }, 500);
          }
        }
      }
    };

    // 类型断言来处理事件监听器
    const eventListener = handleTaskDeleted as EventListener;
    window.addEventListener('task-deleted', eventListener);

    return () => {
      window.removeEventListener('task-deleted', eventListener);
    };
  }, []);

  // 优化后的路由变化监听器
  useEffect(() => {
    if (!user?.uuid || typeof window === 'undefined') return;

    let routeChangeTimeout: NodeJS.Timeout;

    const handleRouteChange = () => {
      console.log('Route changed, checking for cache refresh');

      // 清除之前的定时器
      clearTimeout(routeChangeTimeout);

      // 延迟执行，确保页面完全加载，并且只在缓存过期时刷新
      routeChangeTimeout = setTimeout(() => {
        const cachedTasks = getCachedHistory();
        if (!cachedTasks) {
          console.log('Route change: No cache found, refreshing');
          loadHistoryTasks(false, 'route-change');
        } else {
          console.log('Route change: Valid cache exists, skipping refresh');
        }
      }, 1000); // 延长延迟时间
    };

    window.addEventListener('popstate', handleRouteChange);

    return () => {
      clearTimeout(routeChangeTimeout);
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, [user?.uuid]);

  return (
    <>
      <main className="relative min-h-screen px-4 pb-24">
        {/* Header */}
        <header className="relative z-10 pt-16 text-center space-y-4">
          <h1 className="text-5xl md:text-6xl font-serif font-medium leading-[1.25]">
            {section.title || t('sketchtoimage.header.title')}
          </h1>
          <p className="text-muted-foreground mt-4">
            {section.description || t('sketchtoimage.header.description')}
          </p>
        </header>

        {/* Main Grid */}
        <section className="relative z-10 grid lg:grid-cols-2 items-start gap-10 max-w-6xl mx-auto mt-10 min-h-[500px]">
          {/* Left Panel */}
          <div className="space-y-8">
            {/* Upload Section */}
            <div className="space-y-3">
              <div className="font-serif text-xl font-medium">{t('sketchtoimage.upload.title')}</div>
              <p className="text-sm text-muted-foreground">
                {t('sketchtoimage.upload.description')}
              </p>

              {/* Upload Container */}
              <div className="border-2 border-dashed border-border bg-muted/20 rounded-lg p-4 min-h-[200px]">
                {uploadedImages.length === 0 ? (
                  // Empty state
                  <label
                    htmlFor="uploader"
                    className="flex flex-col items-center justify-center py-8 cursor-pointer transition hover:bg-muted/30 rounded-lg"
                  >
                    <Upload className="w-12 h-12 text-muted-foreground mb-4" />
                    <span className="px-4 py-2 border border-border rounded-md text-sm">
                      {t('sketchtoimage.upload.button')}
                    </span>
                    <span className="mt-3 text-xs text-muted-foreground">
                      {t('sketchtoimage.upload.file_types')} | {t('sketchtoimage.upload.max_size', { maxSize: maxFileSize })}
                    </span>
                  </label>
                ) : (
                  // Images grid state
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">
                        {t('sketchtoimage.upload.uploaded_count', { count: uploadedImages.length, max: maxImages })}
                      </p>
                      {uploadedImages.length < maxImages && (
                        <label
                          htmlFor="uploader"
                          className="flex items-center gap-2 px-3 py-1.5 text-xs border border-border rounded-md cursor-pointer hover:bg-muted/30 transition"
                        >
                          <Plus className="w-3 h-3" />
                          {t('sketchtoimage.upload.add_more')}
                        </label>
                      )}
                    </div>
                    
                    {/* Images Grid */}
                    <div className="grid grid-cols-5 sm:grid-cols-4 gap-2">
                      {uploadedImages.map((img) => (
                        <figure
                          key={img.id}
                          className="relative group rounded-lg overflow-hidden aspect-square"
                        >
                          <img
                            src={img.url}
                            alt={img.name}
                            className="w-full h-full object-cover"
                          />
                          <button
                            onClick={() =>
                              setUploadedImages((prev) =>
                                prev.filter((i) => i.id !== img.id)
                              )
                            }
                            className="absolute top-1 right-1 p-0.5 bg-background/80 hover:bg-background rounded-full opacity-0 group-hover:opacity-100 transition"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </figure>
                      ))}
                      
                      {/* Add more placeholder if less than max */}
                      {uploadedImages.length < maxImages && (
                        <label
                          htmlFor="uploader"
                          className="aspect-square border-2 border-dashed border-border bg-muted/10 rounded-lg flex items-center justify-center cursor-pointer hover:border-ring hover:bg-muted/20 transition"
                        >
                          <div className="text-center">
                            <Plus className="w-5 h-5 text-muted-foreground mx-auto mb-1" />
                            <span className="text-xs text-muted-foreground">Add</span>
                          </div>
                        </label>
                      )}
                    </div>
                    
                    {/* Format info */}
                    <p className="text-xs text-muted-foreground text-center">
                      {t('sketchtoimage.upload.file_types')} | {t('sketchtoimage.upload.max_size', { maxSize: maxFileSize })}
                    </p>
                  </div>
                )}

                <input
                  id="uploader"
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  disabled={uploadedImages.length >= maxImages}
                  onChange={handleUpload}
                  className="hidden"
                />
              </div>
            </div>

            {/* Style Selection */}
            <div className="space-y-4">
              <div>
                <div className="font-serif text-xl font-medium">{t('sketchtoimage.style.title')}</div>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('sketchtoimage.style.description')}
                </p>
              </div>

              {/* Current Selected Style Display - Clickable */}
              <div 
                onClick={() => setIsStyleSectionExpanded(!isStyleSectionExpanded)}
                className="p-3 border border-border rounded-lg bg-background cursor-pointer hover:border-ring hover:shadow-sm transition-all"
              >
                <div className="flex items-center gap-3">
                  <img
                    src={selectedStyleOption?.previewImage}
                    alt={selectedStyleOption?.label}
                    className="w-12 h-12 object-cover rounded-md"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{selectedStyleOption?.label}</div>
                    <div className="text-xs text-muted-foreground">{selectedStyleOption?.description}</div>
                  </div>
                  {/* Up/Down Arrow Icon */}
                  <div className="text-muted-foreground">
                    {isStyleSectionExpanded ? (
                      <ChevronUp className="w-5 h-5" />
                    ) : (
                      <ChevronDown className="w-5 h-5" />
                    )}
                  </div>
                </div>
              </div>

              {/* Style Grid - Collapsible */}
              {isStyleSectionExpanded && (
                <div className="grid grid-cols-3 gap-3">
                  {displayedStyles.map(([style, option]) => (
                    <div
                      key={style}
                      onClick={() => handleStyleSelect(style)}
                      className={clsx(
                        "relative cursor-pointer rounded-lg border-2 overflow-hidden transition-all group aspect-square",
                        selectedStyle === style
                          ? "border-primary shadow-md ring-1 ring-primary/20"
                          : "border-border hover:border-ring hover:shadow-sm"
                      )}
                    >
                      <img
                        src={option.previewImage}
                        alt={option.label}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        loading="eager"
                        decoding="async"
                        onError={(e) => {
                          console.error(`Failed to load style image: ${option.label}`, option.previewImage);
                        }}
                        onLoad={() => {
                          console.log(`Successfully loaded style image: ${option.label}`);
                        }}
                      />
                      {/* Selected Indicator */}
                      {selectedStyle === style && (
                        <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                          <div className="bg-primary text-primary-foreground rounded-full p-1">
                            <Check className="w-4 h-4" />
                          </div>
                        </div>
                      )}
                      {/* Style Label */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
                        <p className="text-white text-xs font-medium text-center">
                          {option.label}
                        </p>
                      </div>
                    </div>
                  ))}

                  {/* More Styles Button */}
                  <div
                    onClick={() => setIsStyleSidebarOpen(true)}
                    className="relative cursor-pointer rounded-lg border-2 border-dashed border-border hover:border-ring hover:bg-muted/20 transition-all group aspect-square flex flex-col items-center justify-center"
                  >
                    <MoreIcon className="w-8 h-8 text-muted-foreground mb-2 group-hover:text-foreground transition-colors" />
                    <p className="text-xs text-muted-foreground text-center group-hover:text-foreground transition-colors">
                      {t('sketchtoimage.style.more_styles')}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Adjust Creativity Section */}
            <div className="space-y-3">
              <div>
                <div className="font-serif text-xl font-medium">{t('sketchtoimage.creativity.title')}</div>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('sketchtoimage.creativity.description')}
                </p>
              </div>
              
              {/* Creativity Slider */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{t('sketchtoimage.creativity.similar')}</span>
                  <span>{t('sketchtoimage.creativity.creative')}</span>
                </div>
                
                {/* Custom Slider */}
                <div className="relative">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={creativity}
                    onChange={(e) => setCreativity(parseInt(e.target.value))}
                    className="w-full h-2 rounded-lg cursor-pointer"
                    style={{
                      background: `linear-gradient(to right, #e5e7eb 0%, #3b82f6 ${creativity}%, #e5e7eb 100%)`,
                      WebkitAppearance: 'none',
                      MozAppearance: 'none',
                      appearance: 'none',
                      outline: 'none'
                    }}
                  />
                  <div 
                    className="absolute top-1/2 w-4 h-4 bg-white border-2 border-blue-500 rounded-full transform -translate-y-1/2 pointer-events-none shadow-md"
                    style={{ left: `calc(${creativity}% - 8px)` }}
                  />
                </div>
                
                {/* Current Value Indicator */}
                <div className="text-center">
                  <span className="text-xs text-muted-foreground">
                    {t('sketchtoimage.creativity.current')}: {creativity}% {creativity <= 30 ? t('sketchtoimage.creativity.similar') : creativity >= 70 ? t('sketchtoimage.creativity.creative') : t('sketchtoimage.creativity.balanced')}
                  </span>
                </div>
              </div>
            </div>

            {/* Aspect Ratio */}
            <div className="space-y-3">
              <div className="font-serif text-xl font-medium">{t('sketchtoimage.aspectRatio.title')}</div>
              <p className="text-sm text-muted-foreground">
                {t('sketchtoimage.aspectRatio.description')}
              </p>
              <div className="grid grid-cols-3 gap-3">
                {(["1:1", "3:2", "2:3"] as const).map((r) => (
                  <button
                    key={r}
                    onClick={() => setSelectedAspect(r)}
                    className={clsx(
                      "rounded-md border flex flex-col items-center justify-center gap-1 py-2 transition",
                      selectedAspect === r
                        ? "bg-accent border-ring"
                        : "bg-muted/20 border-border hover:border-ring hover:bg-muted/30"
                    )}
                  >
                    <div
                      className={clsx(
                        "bg-muted-foreground rounded-sm",
                        r === "1:1" && "w-7 h-7",
                        r === "3:2" && "w-9 h-6",
                        r === "2:3" && "w-6 h-9"
                      )}
                    />
                    <span className="text-sm">{r}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerate}
              disabled={isGenerating || !uploadedImages.length}
              className={clsx(
                "w-full flex justify-center items-center gap-2 h-12 rounded-md text-primary-foreground transition",
                uploadedImages.length > 0 
                  ? "bg-primary hover:bg-primary/90" 
                  : "bg-gray-400 cursor-not-allowed",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  {t('sketchtoimage.generate.generating')}
                </>
              ) : uploadedImages.length > 0 ? (
                <>
                  <Sparkles className="w-4 h-4" />
                  {user ? (
                    hasEnoughCredits ? (
                      <div className="flex items-center gap-1">
                        <span>{t('sketchtoimage.generate.button')}</span>
                        <span className="flex items-center gap-1">
                          (<Image src="/svg/coins-need.svg" alt="Credits" width={14} height={14} className="text-yellow-500" />
                          {creditsNeeded})
                        </span>
                      </div>
                    ) : (
                      t('sketchtoimage.generate.insufficient_credits')
                    )
                  ) : (
                    t('sketchtoimage.generate.button')
                  )}
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4" />
                  {t('sketchtoimage.generate.button')}
                </>
              )}
            </button>
          </div>

          {/* Right Panel - Recent Tasks or Examples */}
          <div className="lg:sticky lg:top-40 flex flex-col mt-[65px]">
            <div className="bg-background border border-border rounded-lg overflow-hidden">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
                <div className="font-serif text-xl font-medium">
                  {user ? (section.examples?.recent_tasks_title || t('sketchtoimage.examples.recent_tasks_title')) : (section.examples?.panel_title || t('sketchtoimage.examples.panel_title'))}
                </div>
                {user ? (
                  <div className="flex items-center gap-2">
                    <button 
                      onClick={refreshHistory}
                      className="text-muted-foreground hover:text-foreground text-sm flex items-center gap-1 transition-colors hover:bg-muted/50 px-2 py-1 rounded cursor-pointer"
                      title="Refresh history"
                    >
                      <RefreshCw className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={handleViewAllCreations}
                      className="text-muted-foreground hover:text-foreground text-sm flex items-center gap-1 transition-colors hover:bg-muted/50 px-2 py-1 rounded cursor-pointer"
                    >
                      {section.examples?.view_all || t('sketchtoimage.examples.view_all')}
                      <Clock className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>
                      {currentExampleIndex + 1} {section.examples?.navigation?.of || "/"} {exampleImages.length}
                    </span>
                  </div>
                )}
              </div>

              {/* Content Area */}
              <div className="max-h-[600px] overflow-y-auto">
                {user ? (
                  <>
                    {/* Logged in user - Recent Tasks */}
                    {displayTasks.length === 0 && !isLoadingHistory ? (
                      <div className="p-8 text-center">
                        <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                          <Sparkles className="w-8 h-8 text-muted-foreground" />
                        </div>
                        <p className="text-muted-foreground">
                          {section.examples?.no_tasks || t('sketchtoimage.examples.no_tasks')}
                        </p>
                        <p className="text-muted-foreground/60 text-sm mt-1">
                          {section.examples?.no_tasks_description || t('sketchtoimage.examples.no_tasks_description')}
                        </p>
                      </div>
                    ) : isLoadingHistory ? (
                      <div className="p-8 text-center">
                        <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {section.examples?.loading_history || t('sketchtoimage.examples.loading_history')}
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-3 p-4">
                        {displayTasks.map((task) => (
                          <div key={task.id} className="bg-card border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                            {/* 上边框效果 */}
                            <div className="w-full h-[6px] rounded-full bg-gradient-to-r from-zinc-900 via-gray-300 to-zinc-900" />
                            
                            {/* 卡片内容 */}
                            <div className="p-4">
                              {/* Task Header */}
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <span>{formatTime(task.timestamp)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  {getStatusIcon(task.status)}
                                  <span className={clsx(
                                    "text-xs font-medium px-2 py-1 rounded",
                                    task.status === 'completed' ? "text-green-600 bg-green-100" :
                                    task.status === 'generating' ? "text-blue-600 bg-blue-100" :
                                    "text-red-600 bg-red-100"
                                  )}>
                                    {getStatusText(task.status)}
                                  </span>
                                </div>
                              </div>

                              {/* Style Info */}
                              <div className="flex items-center gap-2 mb-3 text-sm">
                                <span className="text-foreground">
                                  {t('sketchtoimage.style.style')}: {section.style.options[task.style]?.label || task.style}
                                </span>
                                <span className="text-muted-foreground">•</span>
                                <span className="text-foreground">
                                  {t('sketchtoimage.aspectRatio.ratio')}: {task.aspectRatio}
                                </span>
                              </div>

                              {/* Images Grid */}
                              <div className="grid grid-cols-4 gap-1.5 mb-3">
                                {task.results.map((result) => {
                                  return (
                                    <div key={result.id} className="relative">
                                      {result.status === 'generating' ? (
                                        <div className="space-y-2">
                                          <div className="aspect-square bg-gradient-to-br from-blue-50 to-purple-50 rounded-md flex items-center justify-center relative overflow-hidden">
                                            <div className="absolute inset-0 bg-gradient-to-r from-blue-200 via-purple-200 to-blue-200 animate-pulse opacity-60"></div>
                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-[shimmer_2s_infinite] opacity-40"></div>
                                            
                                            <div className="relative z-10 flex flex-col items-center gap-1">
                                              <div className="relative">
                                                <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                                                <div className="absolute inset-0 w-4 h-4 border border-purple-300 border-t-transparent rounded-full animate-ping"></div>
                                              </div>
                                              <div className="w-6 h-0 bg-blue-200 rounded-full overflow-hidden">
                                                <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-[progress_2s_infinite]"
                                                     style={{
                                                       animation: 'progress 2s infinite',
                                                       width: '100%',
                                                       transform: 'translateX(-100%)'
                                                     }}></div>
                                              </div>
                                            </div>
                                            
                                            <div className="absolute top-1 right-1 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
                                            <div className="absolute bottom-1 left-1 w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse delay-500"></div>
                                          </div>
                                        </div>
                                      ) : result.status === 'error' ? (
                                        <div className="space-y-2">
                                          <div className="aspect-square bg-red-50 rounded-md flex items-center justify-center">
                                            <AlertCircle className="w-4 h-4 text-red-500" />
                                          </div>
                                          <div className="flex gap-1">
                                            <button className="text-xs px-2 py-1 bg-red-50 text-red-600 rounded flex-1 cursor-not-allowed">
                                              {section.actions?.preview || t('sketchtoimage.actions.preview')}
                                            </button>
                                            <button className="text-xs px-2 py-1 bg-red-50 text-red-600 rounded flex-1 cursor-not-allowed">
                                              {section.actions?.download || t('sketchtoimage.actions.download')}
                                            </button>
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="aspect-square rounded-md overflow-hidden border border-border hover:border-primary/50 transition-colors relative group">
                                          <div className="relative w-full h-full">
                                            <img
                                              src={formatImageUrl(result.url)}
                                              alt={section.alt_text?.generated_sketch || t('sketchtoimage.alt_text.generated_sketch')}
                                              className="w-full h-full object-cover"
                                              onError={() => {
                                                handleImageError(result.id, result.url);
                                              }}
                                              onLoad={() => {
                                                handleImageLoad(result.id);
                                              }}
                                            />

                                            {imageLoadErrors.has(result.id) && (
                                              <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                                                <div className="text-center p-2">
                                                  <AlertCircle className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                                                  <p className="text-xs text-gray-500 mb-2">{section.errors?.failed_to_load || t('sketchtoimage.errors.failed_to_load')}</p>
                                                  <button 
                                                    onClick={() => {
                                                      setImageLoadErrors(prev => {
                                                        const newSet = new Set(prev);
                                                        newSet.delete(result.id);
                                                        return newSet;
                                                      });
                                                      const img = document.querySelector(`img[alt="${section.alt_text?.generated_sketch || t('sketchtoimage.alt_text.generated_sketch')}"]`) as HTMLImageElement;
                                                      if (img) {
                                                        img.src = img.src;
                                                      }
                                                    }}
                                                    className="text-xs text-blue-500 hover:text-blue-700 px-2 py-1 border border-blue-200 rounded"
                                                  >
                                                    {section.actions?.retry || t('sketchtoimage.actions.retry')}
                                                  </button>
                                                </div>
                                              </div>
                                            )}
                                          </div>

                                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-2">
                                            <div className="flex gap-1">
                                              <button
                                                onClick={() => previewImage(formatImageUrl(result.url), `${t('sketchtoimage.preview.generated_sketch')}${task.style}`)}
                                                className="text-xs px-2 py-1 bg-white/90 text-gray-800 rounded hover:bg-white transition-colors flex-1 flex items-center justify-center gap-1 backdrop-blur-sm"
                                                title={section.tooltips?.preview_image || t('sketchtoimage.tooltips.preview_image')}
                                              >
                                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                              </button>
                                              <button
                                                onClick={() => downloadImage(formatImageUrl(result.url), `sketch_${task.style}_${Date.now()}.png`)}
                                                className="text-xs px-2 py-1 bg-white/90 text-gray-800 rounded hover:bg-white transition-colors flex-1 flex items-center justify-center gap-1 backdrop-blur-sm"
                                                title={section.tooltips?.download_image || t('sketchtoimage.tooltips.download_image')}
                                              >
                                                <Download className="w-3 h-3" />
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  );
                                })}
                              </div>

                              {/* Task Actions */}
                              {task.status === 'completed' && (
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={() => handleRegenerate(task)}
                                    className="flex items-center gap-2 px-3 py-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors text-sm"
                                  >
                                    <RefreshCw className="w-3 h-3" />
                                    {section.actions?.regenerate || t('sketchtoimage.actions.regenerate')}
                                  </button>
                                  <button
                                    onClick={() => deleteTask(task.id)}
                                    className="flex items-center gap-2 px-3 py-1.5 bg-red-50 text-red-600 rounded-md hover:bg-red-100 transition-colors text-sm"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    {/* Non-logged in user - Examples Carousel */}
                    <div className="p-4">
                      {/* Current Example Image */}
                      <div className="relative mb-4">
                        <div className="aspect-[4/3] relative overflow-hidden rounded-lg">
                          <Image
                            src={exampleImages[currentExampleIndex].url}
                            alt={exampleImages[currentExampleIndex].title}
                            fill
                            className="object-cover"
                            onError={() => {
                              console.error('Example image failed to load:', exampleImages[currentExampleIndex].url);
                            }}
                          />
                          
                          {/* Navigation Arrows */}
                          <button
                            onClick={prevExample}
                            className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors"
                          >
                            <ChevronLeft className="w-4 h-4" />
                          </button>
                          <button
                            onClick={nextExample}
                            className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors"
                          >
                            <ChevronRight className="w-4 h-4" />
                          </button>
                        </div>
                        
                        {/* Dots Indicator */}
                        <div className="flex justify-center mt-3 gap-2">
                          {Array.from({ length: exampleImages.length }).map((_, index: number) => (
                            <button
                              key={index}
                              onClick={() => setCurrentExampleIndex(index)}
                              className={`w-2 h-2 rounded-full transition-colors ${
                                index === currentExampleIndex ? 'bg-primary' : 'bg-muted-foreground/30'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      
                      {/* Example Info */}
                      <div className="space-y-3">
                        <div>
                          <div className="font-semibold text-lg mb-1">
                            {exampleImages[currentExampleIndex].title}
                          </div>
                          <p className="text-muted-foreground text-sm">
                            {exampleImages[currentExampleIndex].description}
                          </p>
                        </div>
                        
                        {/* Sign in CTA */}
                        <div className="p-3 bg-muted/30 rounded-lg border border-dashed border-muted-foreground/30">
                          <p className="text-sm text-muted-foreground mb-2">
                            {section.examples?.cta?.title || t('sketchtoimage.examples.cta.title')}
                          </p>
                          <button
                            onClick={() => setShowSignModal(true)}
                            className="text-sm bg-primary text-primary-foreground px-3 py-1.5 rounded-md hover:bg-primary/90 transition-colors w-full"
                          >
                            {section.examples?.cta?.button || t('sketchtoimage.examples.cta.button')}
                          </button>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>
      <ImageToSketchBg />

      {/* 大尺寸预览模态框 */}
      {previewModal.isOpen && (
        <div 
          className="fixed inset-0 bg-black/85 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setPreviewModal({ isOpen: false, imageUrl: "", title: "" })}
        >
          {/* 大尺寸模态框容器 */}
          <div 
            className="relative max-w-4xl max-h-[85vh] w-full bg-white rounded-xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部标题栏 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
              <div className="font-semibold text-gray-900 truncate pr-4">
                {section.preview?.modal?.title || t('sketchtoimage.preview.modal.title')}
              </div>
              {/* 明显的关闭按钮 */}
              <button
                onClick={() => setPreviewModal({ isOpen: false, imageUrl: "", title: "" })}
                className="flex-shrink-0 w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-full transition-all"
                title={section.preview?.modal?.close || t('sketchtoimage.preview.modal.close')}
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            {/* 大尺寸图片容器 */}
            <div className="relative bg-gray-100 flex items-center justify-center">
              <img
                src={formatImageUrl(previewModal.imageUrl)}
                alt={previewModal.title}
                className="w-full h-auto max-h-[65vh] object-contain"
                onError={(e) => {
                  console.error('Preview image failed to load:', previewModal.imageUrl);
                }}
              />
            </div>
            
            {/* 底部操作区 */}
            <div className="p-4 bg-gray-50 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">
                  {section.preview?.modal?.closeHint || t('sketchtoimage.preview.modal.close_hint')}
                </span>
                <div className="flex gap-3">
                  {/* 下载按钮 */}
                                      <button
                      onClick={() => {
                        const downloadFilename = (section.preview?.modal?.downloadFilename || `sketch_{timestamp}.png`)
                          .replace('{timestamp}', Date.now().toString());
                        downloadImage(previewModal.imageUrl, downloadFilename);
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    {section.preview?.modal?.download || t('sketchtoimage.preview.modal.download')}
                  </button>
                  {/* 关闭按钮 */}
                  <button
                    onClick={() => setPreviewModal({ isOpen: false, imageUrl: "", title: "" })}
                    className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    {section.preview?.modal?.close || t('sketchtoimage.preview.modal.close')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Style Sidebar */}
      {isStyleSidebarOpen && (
        <div className="fixed inset-0 z-50 flex">
          {/* Sidebar */}
          <div className="w-96 bg-background border-r border-border shadow-xl overflow-hidden flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
              <div className="font-serif text-lg font-medium">{t('sketchtoimage.style.all_styles')}</div>
              <button
                onClick={() => setIsStyleSidebarOpen(false)}
                className="w-8 h-8 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-muted rounded-full transition-all"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Styles Grid */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="grid grid-cols-2 gap-3">
                {availableStyles.map(([style, option]) => (
                  <div
                    key={style}
                    onClick={() => handleStyleSelect(style)}
                    className={clsx(
                      "relative cursor-pointer rounded-lg border-2 overflow-hidden transition-all group",
                      selectedStyle === style
                        ? "border-primary shadow-md ring-1 ring-primary/20"
                        : "border-border hover:border-ring hover:shadow-sm"
                    )}
                  >
                    {/* Preview Image */}
                    <div className="relative aspect-square overflow-hidden">
                      <img
                        src={option.previewImage}
                        alt={option.label}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        loading="eager"
                        decoding="async"
                        onError={(e) => {
                          console.error(`Failed to load sidebar style image: ${option.label}`, option.previewImage);
                        }}
                        onLoad={() => {
                          console.log(`Successfully loaded sidebar style image: ${option.label}`);
                        }}
                      />
                      {/* Selected Indicator */}
                      {selectedStyle === style && (
                        <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                          <div className="bg-primary text-primary-foreground rounded-full p-1">
                            <Check className="w-4 h-4" />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Style Info */}
                    <div className="p-3">
                      <h4 className="font-medium text-sm leading-tight mb-1">
                        {option.label}
                      </h4>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {option.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Backdrop */}
          <div 
            className="flex-1 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsStyleSidebarOpen(false)}
          />
        </div>
      )}
    </>
  );
}
        
