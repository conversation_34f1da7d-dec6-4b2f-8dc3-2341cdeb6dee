# Environment Variables Configuration

This document describes the environment variables needed for the email authentication feature.

## Required Environment Variables for Email Authentication

### Authentication Configuration
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here
```

### Email Provider (Magic Link)
```env
NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true
RESEND_API_KEY=your-resend-api-key
NEXT_PUBLIC_EMAIL_FROM=<EMAIL>
```

### Other OAuth Providers (existing)
```env
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret
```

## Setup Instructions

1. Create a `.env.development` file in the root directory
2. Add the environment variables listed above
3. Configure your Resend account and get the API key
4. Set up your domain for email sending in Resend
5. Configure NEXTAUTH_SECRET with a random string

## Resend Setup

1. Sign up at https://resend.com/
2. Verify your domain or use the sandbox for testing
3. Create an API key in your Resend dashboard
4. Set the API key in your environment variables

## Email Configuration

The email authentication uses Resend SMTP server with NextAuth's built-in EmailProvider:
- SMTP server: smtp.resend.com:587
- Authentication: username "resend" + your Resend API key
- Uses NextAuth's default email template
- 15-minute expiration for magic links

## Testing

For development, you can use:
- `NEXT_PUBLIC_EMAIL_FROM=<EMAIL>` (Resend sandbox)
- Any valid email address for testing

## Production Considerations

- Use your own verified domain for email sending
- Set proper NEXTAUTH_URL for production
- Use secure NEXTAUTH_SECRET
- Monitor email delivery rates in Resend dashboard 