# 邮箱登录（Magic Link）配置指南

## 🎯 功能说明

邮箱登录功能已经完成开发，用户可以通过输入邮箱地址，接收包含登录链接的邮件来完成登录。

## 📋 配置步骤

### 1. 环境变量配置

在你的 `.env.development` 文件中添加以下配置：

```env
# NextAuth 基础配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-32-characters-long

# 启用邮箱登录
NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true

# Resend 邮件服务配置
RESEND_API_KEY=re_your_resend_api_key_here
NEXT_PUBLIC_EMAIL_FROM=<EMAIL>

# 测试环境可以使用 Resend 沙盒邮箱
# NEXT_PUBLIC_EMAIL_FROM=<EMAIL>
```

### 2. 获取 Resend API Key

1. 访问 [https://resend.com/](https://resend.com/) 注册账户
2. 在控制台中创建 API Key
3. 复制 API Key（以 `re_` 开头）
4. 将 API Key 填入环境变量

### 3. 配置发件邮箱

**测试环境：**
- 使用 `NEXT_PUBLIC_EMAIL_FROM=<EMAIL>`
- 无需域名验证，直接可用

**生产环境：**
- 使用你自己的域名，如 `<EMAIL>`
- 需要在 Resend 中验证域名 DNS 记录

### 4. 生成安全密钥

`NEXTAUTH_SECRET` 需要是一个随机字符串，建议32字符以上：

- 在线生成：[https://generate-secret.vercel.app/32](https://generate-secret.vercel.app/32)
- 或使用命令：`openssl rand -base64 32`
- 或手动创建：`myapp2025secretkey123456789abcdef`

## 🚀 使用流程

1. 用户访问登录页面
2. 输入邮箱地址
3. 点击 "Send me my link" 按钮
4. 系统通过 Resend 发送邮件
5. 用户点击邮件中的链接完成登录
6. 系统自动创建或更新用户信息

## 📧 邮件模板

系统使用 NextAuth 的默认邮件模板，包含：
- 清晰的登录链接
- 安全提示信息
- 15分钟过期时间

## 🛠 技术实现

- **邮件服务**：Resend SMTP (smtp.resend.com:587)
- **认证模式**：JWT 模式（不依赖数据库会话）
- **令牌存储**：数据库存储验证令牌
- **用户数据**：自动同步到 Supabase 用户表

## 🧪 测试步骤

1. 确保环境变量配置正确
2. 重启开发服务器：`pnpm run dev`
3. 访问 `http://localhost:3000`
4. 点击登录，应该能看到邮箱输入框
5. 输入有效邮箱地址测试功能

## 🔍 故障排查

### 邮箱登录表单不显示
- 检查 `NEXT_PUBLIC_AUTH_EMAIL_ENABLED=true`
- 确保重启了开发服务器

### 发送邮件失败
- 检查 `RESEND_API_KEY` 是否正确
- 检查 `NEXT_PUBLIC_EMAIL_FROM` 邮箱格式
- 查看开发者控制台的错误信息

### 登录链接无效
- 检查 `NEXTAUTH_URL` 是否与访问地址一致
- 确保 `NEXTAUTH_SECRET` 已配置

## 🔄 数据库迁移

如果需要运行数据库迁移（创建 NextAuth 相关表）：

```bash
# 生成迁移文件（已完成）
pnpm run db:generate

# 执行迁移到数据库
pnpm run db:migrate
```

## 📝 注意事项

- 邮件发送可能需要几秒钟
- 登录链接15分钟后过期
- 测试环境建议使用 Resend 沙盒邮箱
- 生产环境必须验证发件域名

## 🎉 完成状态

✅ NextAuth EmailProvider 已配置  
✅ Resend SMTP 集成完成  
✅ 数据库表结构已创建  
✅ UI 组件已更新  
✅ 国际化文本已配置  
✅ JWT 会话模式已启用  

现在你可以开始配置和测试邮箱登录功能了！ 