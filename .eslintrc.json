{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react/display-name": "off", "react-hooks/exhaustive-deps": "warn", "react-hooks/rules-of-hooks": "warn", "prefer-const": "warn", "@next/next/no-img-element": "warn", "@next/next/no-html-link-for-pages": "warn", "import/no-anonymous-default-export": "warn", "jsx-a11y/alt-text": "warn", "react/jsx-no-comment-textnodes": "warn"}}